{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ContentRenderer.js\";\nimport React from 'react';\nimport { InlineMath, BlockMath } from 'react-katex';\nimport 'katex/dist/katex.min.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContentRenderer = ({\n  text\n}) => {\n  // Handle undefined, null, or empty text\n  if (!text || typeof text !== 'string') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 16\n    }, this);\n  }\n  const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\n  const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\n  // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\n  const boldTextRegex = /\\*\\*.*?\\*\\*/g;\n  // console.log('Text: ', text);\n  let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\n  const lines = modifiedText.split('\\n');\n  // console.log('Lines with symbol: ', lines);\n  const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n  // console.log('Lines: ', restoredLines);\n\n  const inlineMathSymbol = \"~~INLINEMATH~~\";\n  const blockMathSymbol = \"~~BLOCKMATH~~\";\n  const boldSymbol = \"~~BOLD~~\";\n  let newModifiedText = text.replace(blockMathRegex, match => {\n    return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\n    return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(boldTextRegex, match => {\n    // console.log('Bold Part: ', match);\n    return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\n  });\n  const newLines = newModifiedText.split('\\n');\n  const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n\n  // console.log('New Modified Text: ', newModifiedText);\n\n  const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\n\n  // Debug logging removed to prevent React rendering issues\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: newRestoredLines.map((line, lineIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: line.trim() === '' ? /*#__PURE__*/_jsxDEV(\"br\", {}, `br-${lineIndex}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 25\n      }, this) : line.split(newRegex).map((part, index) => {\n        if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\n              if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(InlineMath, {\n                  children: nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 53\n                }, this);\n              } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(BlockMath, {\n                  children: nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 53\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: nestedPart\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 79,\n                    columnNumber: 57\n                  }, this)\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 53\n                }, this);\n              }\n            })\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(InlineMath, {\n            children: part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(BlockMath, {\n            children: part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 37\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              whiteSpace: 'pre-wrap'\n            },\n            children: part\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 37\n          }, this);\n        }\n      })\n    }, lineIndex, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 9\n  }, this);\n};\n_c = ContentRenderer;\nexport default ContentRenderer;\nvar _c;\n$RefreshReg$(_c, \"ContentRenderer\");", "map": {"version": 3, "names": ["React", "InlineMath", "BlockMath", "jsxDEV", "_jsxDEV", "Content<PERSON><PERSON><PERSON>", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "inlineMathRegex", "blockMathRegex", "boldTextRegex", "modifiedText", "replace", "match", "lines", "split", "restoredLines", "map", "line", "inlineMathSymbol", "blockMathSymbol", "boldSymbol", "newModifiedText", "newLines", "newRestoredLines", "newRegex", "children", "lineIndex", "trim", "part", "index", "startsWith", "endsWith", "Fragment", "nested<PERSON><PERSON>", "n_index", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ContentRenderer.js"], "sourcesContent": ["import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    // Debug logging removed to prevent React rendering issues\r\n\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,SAAS,QAAQ,aAAa;AACnD,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAClC;EACA,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnC,oBAAOF,OAAA;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC;EACtB;EAEA,MAAMC,eAAe,GAAG,cAAc;EACtC,MAAMC,cAAc,GAAG,eAAe;EACtC;EACA,MAAMC,aAAa,GAAG,cAAc;EACpC;EACA,IAAIC,YAAY,GAAGR,IAAI,CAACS,OAAO,CAACH,cAAc,EAAEI,KAAK,IAAIA,KAAK,CAACD,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;EAC7F,MAAME,KAAK,GAAGH,YAAY,CAACI,KAAK,CAAC,IAAI,CAAC;EACtC;EACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACN,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;EAC7E;;EAKA,MAAMO,gBAAgB,GAAG,gBAAgB;EACzC,MAAMC,eAAe,GAAG,eAAe;EACvC,MAAMC,UAAU,GAAG,UAAU;EAE7B,IAAIC,eAAe,GAAGnB,IAAI,CAACS,OAAO,CAACH,cAAc,EAAEI,KAAK,IAAI;IACxD,OAAQ,gBAAeA,KAAK,CAACD,OAAO,CAAC,KAAK,EAAE,aAAa,CAAE,eAAc;EAC7E,CAAC,CAAC;EAEFU,eAAe,GAAGA,eAAe,CAACV,OAAO,CAACJ,eAAe,EAAEK,KAAK,IAAI;IAChE,OAAQ,iBAAgBA,KAAM,gBAAe;EACjD,CAAC,CAAC;EAEFS,eAAe,GAAGA,eAAe,CAACV,OAAO,CAACF,aAAa,EAAEG,KAAK,IAAI;IAC9D;IACA,OAAQ,WAAUA,KAAK,CAACD,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,UAAS;EAC1D,CAAC,CAAC;EAEF,MAAMW,QAAQ,GAAGD,eAAe,CAACP,KAAK,CAAC,IAAI,CAAC;EAE5C,MAAMS,gBAAgB,GAAGD,QAAQ,CAACN,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACN,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;;EAEnF;;EAEA,MAAMa,QAAQ,GAAG,yGAAyG;;EAE1H;;EAEA,oBACIxB,OAAA;IAAAyB,QAAA,EACKF,gBAAgB,CAACP,GAAG,CAAC,CAACC,IAAI,EAAES,SAAS,kBAClC1B,OAAA;MAAAyB,QAAA,EACKR,IAAI,CAACU,IAAI,CAAC,CAAC,KAAK,EAAE,gBACf3B,OAAA,WAAU,MAAK0B,SAAU,EAAC;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GAE9BW,IAAI,CAACH,KAAK,CAACU,QAAQ,CAAC,CAACR,GAAG,CAAC,CAACY,IAAI,EAAEC,KAAK,KAAK;QACtC,IAAID,IAAI,CAACE,UAAU,CAACV,UAAU,CAAC,IAAIQ,IAAI,CAACG,QAAQ,CAACX,UAAU,CAAC,EAAE;UAC1D,oBACIpB,OAAA,CAACJ,KAAK,CAACoC,QAAQ;YAAAP,QAAA,EACVG,IAAI,CAACjB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACG,KAAK,CAACU,QAAQ,CAAC,CAACR,GAAG,CAAC,CAACiB,UAAU,EAAEC,OAAO,KAAK;cACxE,IAAID,UAAU,CAACH,UAAU,CAACZ,gBAAgB,CAAC,IAAIe,UAAU,CAACF,QAAQ,CAACb,gBAAgB,CAAC,EAAE;gBAClF,oBACIlB,OAAA,CAACH,UAAU;kBAAA4B,QAAA,EACNQ,UAAU,CAACtB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;gBAAC,GADxD,GAAEe,SAAU,IAAGG,KAAM,IAAGK,OAAQ,EAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAErB,CAAC,MAAM,IAAI2B,UAAU,CAACH,UAAU,CAACX,eAAe,CAAC,IAAIc,UAAU,CAACF,QAAQ,CAACZ,eAAe,CAAC,EAAE;gBACvF,oBACInB,OAAA,CAACF,SAAS;kBAAA2B,QAAA,EACLQ,UAAU,CAACtB,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;gBAAC,GADrD,GAAEe,SAAU,IAAGG,KAAM,IAAGK,OAAQ,EAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEpB,CAAC,MAAM;gBACH,oBACIN,OAAA;kBAA+CmC,KAAK,EAAE;oBAAEC,UAAU,EAAE;kBAAW,CAAE;kBAAAX,QAAA,eAC7EzB,OAAA;oBAAAyB,QAAA,EAASQ;kBAAU;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC,GADrB,GAAEoB,SAAU,IAAGG,KAAM,IAAGK,OAAQ,EAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEf;YACJ,CAAC;UAAC,GArBgB,GAAEoB,SAAU,IAAGG,KAAM,EAAC;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsB5B,CAAC;QAEzB,CAAC,MAAM,IAAIsB,IAAI,CAACE,UAAU,CAACZ,gBAAgB,CAAC,IAAIU,IAAI,CAACG,QAAQ,CAACb,gBAAgB,CAAC,EAAE;UAC7E,oBACIlB,OAAA,CAACH,UAAU;YAAA4B,QAAA,EACNG,IAAI,CAACjB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;UAAC,GADlD,GAAEe,SAAU,IAAGG,KAAM,EAAC;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAErB,CAAC,MAAM,IAAIsB,IAAI,CAACE,UAAU,CAACX,eAAe,CAAC,IAAIS,IAAI,CAACG,QAAQ,CAACZ,eAAe,CAAC,EAAE;UAC3E,oBACInB,OAAA,CAACF,SAAS;YAAA2B,QAAA,EACLG,IAAI,CAACjB,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;UAAC,GAD/C,GAAEe,SAAU,IAAGG,KAAM,EAAC;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEpB,CAAC,MAAM;UACH,oBACIN,OAAA;YAAoCmC,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAW,CAAE;YAAAX,QAAA,EACjEG;UAAI,GADG,GAAEF,SAAU,IAAGG,KAAM,EAAC;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEf;MACJ,CAAC;IAAC,GAlDAoB,SAAS;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmDd,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAGd,CAAC;AAAC+B,EAAA,GA1GIpC,eAAe;AA4GrB,eAAeA,eAAe;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}