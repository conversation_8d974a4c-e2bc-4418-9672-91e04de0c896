{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport Flag from \"../assets/tanzania-flag.png\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport { useTheme } from \"../contexts/ThemeContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const adminMenu = [{\n    title: \"Users\",\n    paths: [\"/admin/users\", \"/admin/users/add\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-file-list-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/users\")\n  }, {\n    title: \"Exams\",\n    paths: [\"/admin/exams\", \"/admin/exams/add\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-file-list-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/exams\")\n  }, {\n    title: \"AI Questions\",\n    paths: [\"/admin/ai-questions\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-robot-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/ai-questions\")\n  }, {\n    title: \"Study Materials\",\n    paths: [\"/admin/study-materials\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-book-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/study-materials\")\n  }, {\n    title: \"Reports\",\n    paths: [\"/admin/reports\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-bar-chart-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/reports\")\n  }, {\n    title: \"Forum\",\n    paths: [\"/forum\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-discuss-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/forum\")\n  }, {\n    title: \"Profile\",\n    paths: [\"/profile\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-user-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/profile\")\n  }, {\n    title: \"Announcements\",\n    paths: [\"/admin/announcements\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-notification-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/announcements\")\n  }, {\n    title: \"Logout\",\n    paths: [\"/logout\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-logout-box-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this),\n    onClick: () => {\n      localStorage.removeItem(\"token\");\n      navigate(\"/login\");\n    }\n  }];\n  const getUserData = async () => {\n    try {\n      console.log('Getting user data...'); // Debug log\n      const response = await getUserInfo();\n      console.log('User data response:', response); // Debug log\n\n      if (response.success) {\n        dispatch(SetUser(response.data));\n        if (response.data.isAdmin) {\n          setMenu(adminMenu);\n        } else {\n          setMenu(userMenu);\n        }\n      } else {\n        console.error('Failed to get user data:', response.message); // Debug log\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      console.error('Error getting user data:', error); // Debug log\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    // Function to handle resizing\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n      setCollapsed(window.innerWidth < 768);\n    };\n\n    // Add resize event listener\n    window.addEventListener(\"resize\", handleResize);\n    if (window.innerWidth < 768) {\n      setIsMobile(true);\n      setCollapsed(true);\n    }\n\n    // Check for token and navigate\n    const token = localStorage.getItem(\"token\");\n    console.log('Token check:', token ? 'Token exists' : 'No token found'); // Debug log\n\n    if (token) {\n      getUserData();\n    } else {\n      console.log('No token, redirecting to login'); // Debug log\n      navigate(\"/login\");\n    }\n\n    // Cleanup the event listener when the component is unmounted\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n  const getIsActiveOrNot = paths => {\n    if (paths.includes(activeRoute)) {\n      return true;\n    } else {\n      if (activeRoute.includes(\"/admin/exams/edit\") && paths.includes(\"/admin/exams\")) {\n        return true;\n      }\n      if (activeRoute.includes(\"/user/write-exam\") && paths.includes(\"/user/write-exam\")) {\n        return true;\n      }\n    }\n    return false;\n  };\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: \"nav-modern bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-30 shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"BrainWave Educational Platform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-semibold text-gray-900\",\n                  children: user === null || user === void 0 ? void 0 : user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-600 font-medium\",\n                  children: user !== null && user !== void 0 && user.isAdmin ? \"Administrator\" : `Class ${user === null || user === void 0 ? void 0 : user.class} Student`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: Flag,\n                alt: \"Tanzania Flag\",\n                className: \"w-6 h-4 rounded border border-gray-200 shadow-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 232,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"NaPv4M1ve+2f5F0Lvw7be0jxD54=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useTheme];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "Flag", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "useTheme", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "adminMenu", "title", "paths", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "localStorage", "removeItem", "getUserData", "console", "log", "response", "success", "data", "isAdmin", "setMenu", "userMenu", "error", "handleResize", "setIsMobile", "window", "innerWidth", "setCollapsed", "addEventListener", "token", "getItem", "removeEventListener", "getIsActiveOrNot", "includes", "activeRoute", "verifyPaymentStatus", "paymentStatus", "current", "clearInterval", "paymentRequired", "setInterval", "getButtonClass", "isDarkMode", "toggleTheme", "header", "initial", "y", "opacity", "animate", "name", "class", "src", "alt", "div", "transition", "duration", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Flag from \"../assets/tanzania-flag.png\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport { useTheme } from \"../contexts/ThemeContext\";\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n\r\n\r\n\r\n  const adminMenu = [\r\n    {\r\n      title: \"Users\",\r\n      paths: [\"/admin/users\", \"/admin/users/add\"],\r\n      icon: <i className=\"ri-file-list-line\"></i>,\r\n      onClick: () => navigate(\"/admin/users\"),\r\n    },\r\n    {\r\n      title: \"Exams\",\r\n      paths: [\"/admin/exams\", \"/admin/exams/add\"],\r\n      icon: <i className=\"ri-file-list-line\"></i>,\r\n      onClick: () => navigate(\"/admin/exams\"),\r\n    },\r\n    {\r\n      title: \"AI Questions\",\r\n      paths: [\"/admin/ai-questions\"],\r\n      icon: <i className=\"ri-robot-line\"></i>,\r\n      onClick: () => navigate(\"/admin/ai-questions\"),\r\n    },\r\n    {\r\n      title: \"Study Materials\",\r\n      paths: [\"/admin/study-materials\"],\r\n      icon: <i className=\"ri-book-line\"></i>,\r\n      onClick: () => navigate(\"/admin/study-materials\"),\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      paths: [\"/admin/reports\"],\r\n      icon: <i className=\"ri-bar-chart-line\"></i>,\r\n      onClick: () => navigate(\"/admin/reports\"),\r\n    },\r\n    {\r\n      title: \"Forum\",\r\n      paths: [\"/forum\"],\r\n      icon: <i className=\"ri-discuss-line\"></i>,\r\n      onClick: () => navigate(\"/forum\"),\r\n    },\r\n    {\r\n      title: \"Profile\",\r\n      paths: [\"/profile\"],\r\n      icon: <i className=\"ri-user-line\"></i>,\r\n      onClick: () => navigate(\"/profile\"),\r\n    },\r\n    {\r\n      title: \"Announcements\",\r\n      paths: [\"/admin/announcements\"],\r\n      icon: <i className=\"ri-notification-line\"></i>,\r\n      onClick: () => navigate(\"/admin/announcements\"),\r\n    },\r\n    {\r\n      title: \"Logout\",\r\n      paths: [\"/logout\"],\r\n      icon: <i className=\"ri-logout-box-line\"></i>,\r\n      onClick: () => {\r\n        localStorage.removeItem(\"token\");\r\n        navigate(\"/login\");\r\n      },\r\n    },\r\n  ];\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      console.log('Getting user data...'); // Debug log\r\n      const response = await getUserInfo();\r\n      console.log('User data response:', response); // Debug log\r\n\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n        if (response.data.isAdmin) {\r\n          setMenu(adminMenu);\r\n        } else {\r\n          setMenu(userMenu);\r\n        }\r\n      } else {\r\n        console.error('Failed to get user data:', response.message); // Debug log\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error getting user data:', error); // Debug log\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Function to handle resizing\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n      setCollapsed(window.innerWidth < 768);\r\n    };\r\n\r\n    // Add resize event listener\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n      setCollapsed(true);\r\n    }\r\n\r\n    // Check for token and navigate\r\n    const token = localStorage.getItem(\"token\");\r\n    console.log('Token check:', token ? 'Token exists' : 'No token found'); // Debug log\r\n\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      console.log('No token, redirecting to login'); // Debug log\r\n      navigate(\"/login\");\r\n    }\r\n\r\n    // Cleanup the event listener when the component is unmounted\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const getIsActiveOrNot = (paths) => {\r\n    if (paths.includes(activeRoute)) {\r\n      return true;\r\n    } else {\r\n      if (\r\n        activeRoute.includes(\"/admin/exams/edit\") &&\r\n        paths.includes(\"/admin/exams\")\r\n      ) {\r\n        return true;\r\n      }\r\n      if (\r\n        activeRoute.includes(\"/user/write-exam\") &&\r\n        paths.includes(\"/user/write-exam\")\r\n      ) {\r\n        return true;\r\n      }\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* No sidebar - users will use hub for navigation */}\r\n\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Header */}\r\n        <motion.header\r\n          initial={{ y: -20, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"nav-modern bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-30 shadow-sm\"\r\n        >\r\n          <div className=\"px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"flex items-center justify-between h-16\">\r\n              {/* Left Section - Page Title */}\r\n              <div className=\"flex items-center\">\r\n                <h1 className=\"text-xl font-bold text-gray-900\">\r\n                  BrainWave Educational Platform\r\n                </h1>\r\n              </div>\r\n\r\n              {/* Right Section - User Profile */}\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"text-right\">\r\n                  <div className=\"text-sm font-semibold text-gray-900\">{user?.name}</div>\r\n                  <div className=\"text-xs text-gray-600 font-medium\">\r\n                    {user?.isAdmin ? \"Administrator\" : `Class ${user?.class} Student`}\r\n                  </div>\r\n                </div>\r\n                <img src={Flag} alt=\"Tanzania Flag\" className=\"w-6 h-4 rounded border border-gray-200 shadow-sm\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content */}\r\n        <main className=\"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMyB,WAAW,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM;IAAEyB;EAAiB,CAAC,GAAGpB,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACK,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAGtB,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAI9B,MAAMwB,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC3CC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,cAAc;EACxC,CAAC,EACD;IACEE,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC3CC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,cAAc;EACxC,CAAC,EACD;IACEE,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,CAAC,qBAAqB,CAAC;IAC9BC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACvCC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,qBAAqB;EAC/C,CAAC,EACD;IACEE,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,CAAC,wBAAwB,CAAC;IACjCC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACtCC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,wBAAwB;EAClD,CAAC,EACD;IACEE,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,gBAAgB,CAAC;IACzBC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,gBAAgB;EAC1C,CAAC,EACD;IACEE,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,QAAQ,CAAC;IACjBC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACzCC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,QAAQ;EAClC,CAAC,EACD;IACEE,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,UAAU,CAAC;IACnBC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACtCC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,UAAU;EACpC,CAAC,EACD;IACEE,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAC,sBAAsB,CAAC;IAC/BC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC9CC,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,sBAAsB;EAChD,CAAC,EACD;IACEE,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,CAAC,SAAS,CAAC;IAClBC,IAAI,eAAElB,OAAA;MAAGmB,SAAS,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC5CC,OAAO,EAAEA,CAAA,KAAM;MACbC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChCZ,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,CACF;EAED,MAAMa,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC;MACrC,MAAMC,QAAQ,GAAG,MAAM3C,WAAW,CAAC,CAAC;MACpCyC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,QAAQ,CAAC,CAAC,CAAC;;MAE9C,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBlB,QAAQ,CAACvB,OAAO,CAACwC,QAAQ,CAACE,IAAI,CAAC,CAAC;QAChC,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBC,OAAO,CAACnB,SAAS,CAAC;QACpB,CAAC,MAAM;UACLmB,OAAO,CAACC,QAAQ,CAAC;QACnB;MACF,CAAC,MAAM;QACLP,OAAO,CAACQ,KAAK,CAAC,0BAA0B,EAAEN,QAAQ,CAAClD,OAAO,CAAC,CAAC,CAAC;QAC7DA,OAAO,CAACwD,KAAK,CAACN,QAAQ,CAAClD,OAAO,CAAC;QAC/BkC,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC,CAAC,CAAC;MAClDtB,QAAQ,CAAC,QAAQ,CAAC;MAClBlC,OAAO,CAACwD,KAAK,CAACA,KAAK,CAACxD,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd;IACA,MAAMuD,YAAY,GAAGA,CAAA,KAAM;MACzBC,WAAW,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;MACpCC,YAAY,CAACF,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;IACvC,CAAC;;IAED;IACAD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IAE/C,IAAIE,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MAC3BF,WAAW,CAAC,IAAI,CAAC;MACjBG,YAAY,CAAC,IAAI,CAAC;IACpB;;IAEA;IACA,MAAME,KAAK,GAAGlB,YAAY,CAACmB,OAAO,CAAC,OAAO,CAAC;IAC3ChB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEc,KAAK,GAAG,cAAc,GAAG,gBAAgB,CAAC,CAAC,CAAC;;IAExE,IAAIA,KAAK,EAAE;MACThB,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC;MAC/Cf,QAAQ,CAAC,QAAQ,CAAC;IACpB;;IAEA;IACA,OAAO,MAAM;MACXyB,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAER,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,gBAAgB,GAAI7B,KAAK,IAAK;IAClC,IAAIA,KAAK,CAAC8B,QAAQ,CAACC,WAAW,CAAC,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,IACEA,WAAW,CAACD,QAAQ,CAAC,mBAAmB,CAAC,IACzC9B,KAAK,CAAC8B,QAAQ,CAAC,cAAc,CAAC,EAC9B;QACA,OAAO,IAAI;MACb;MACA,IACEC,WAAW,CAACD,QAAQ,CAAC,kBAAkB,CAAC,IACxC9B,KAAK,CAAC8B,QAAQ,CAAC,kBAAkB,CAAC,EAClC;QACA,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAEDjE,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACyC,QAAQ,CAACC,WAAW,CAAC,EAAE;MACrElC,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAE0C,WAAW,EAAElC,QAAQ,CAAC,CAAC;EAE7C,MAAMmC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMjB,IAAI,GAAG,MAAMrC,kBAAkB,CAAC,CAAC;MACvCiC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEG,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,KAAK,IAAI,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,aAAa,MAAK,MAAM,EAAE;QACjD,IAAIzC,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACjB,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACAW,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,QAAQ,CAACjB,eAAe,CAACoC,IAAI,CAAC,CAAC;QAC/B,IAAIxB,WAAW,CAAC2C,OAAO,EAAE;UACvBC,aAAa,CAAC5C,WAAW,CAAC2C,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEO,KAAK,CAAC;MACpDvB,QAAQ,CAACjB,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/BW,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAEDzB,SAAS,CAAC,MAAM;IACd,IAAIsB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiD,eAAe,IAAI,EAACjD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,OAAO,GAAE;MAC3CL,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAIlB,yBAAyB,EAAE;QAC7BiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CrB,WAAW,CAAC2C,OAAO,GAAGG,WAAW,CAAC,MAAM;UACtC1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCoB,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTpC,QAAQ,CAAChB,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACc,yBAAyB,CAAC,CAAC;EAE/B7B,SAAS,CAAC,MAAM;IACd,IAAIsB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiD,eAAe,IAAI,EAACjD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,OAAO,GAAE;MAC3CL,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BoB,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAC7C,IAAI,EAAE4C,WAAW,CAAC,CAAC;EAGvB,MAAMO,cAAc,GAAIvC,KAAK,IAAK;IAChC;IACA,IAAI,CAACZ,IAAI,CAACiD,eAAe,IAAIrC,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAAP,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyC,aAAa,MAAK,MAAM,IAAI9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiD,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAGD,MAAM;IAAEG,UAAU;IAAEC;EAAY,CAAC,GAAG3D,QAAQ,CAAC,CAAC;EAE9C,oBACEE,OAAA;IAAKmB,SAAS,EAAC,0CAA0C;IAAAjB,QAAA,eAKvDF,OAAA;MAAKmB,SAAS,EAAC,mCAAmC;MAAAjB,QAAA,gBAEhDF,OAAA,CAACf,MAAM,CAACyE,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9B1C,SAAS,EAAC,8FAA8F;QAAAjB,QAAA,eAExGF,OAAA;UAAKmB,SAAS,EAAC,sBAAsB;UAAAjB,QAAA,eACnCF,OAAA;YAAKmB,SAAS,EAAC,wCAAwC;YAAAjB,QAAA,gBAErDF,OAAA;cAAKmB,SAAS,EAAC,mBAAmB;cAAAjB,QAAA,eAChCF,OAAA;gBAAImB,SAAS,EAAC,iCAAiC;gBAAAjB,QAAA,EAAC;cAEhD;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGNvB,OAAA;cAAKmB,SAAS,EAAC,6BAA6B;cAAAjB,QAAA,gBAC1CF,OAAA;gBAAKmB,SAAS,EAAC,YAAY;gBAAAjB,QAAA,gBACzBF,OAAA;kBAAKmB,SAAS,EAAC,qCAAqC;kBAAAjB,QAAA,EAAEE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D;gBAAI;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEvB,OAAA;kBAAKmB,SAAS,EAAC,mCAAmC;kBAAAjB,QAAA,EAC/CE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,OAAO,GAAG,eAAe,GAAI,SAAQ7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,KAAM;gBAAS;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvB,OAAA;gBAAKiE,GAAG,EAAE/E,IAAK;gBAACgF,GAAG,EAAC,eAAe;gBAAC/C,SAAS,EAAC;cAAkD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGhBvB,OAAA;QAAMmB,SAAS,EAAC,gEAAgE;QAAAjB,QAAA,eAC9EF,OAAA,CAACf,MAAM,CAACkF,GAAG;UACTR,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BQ,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CnD,SAAS,EAAC,QAAQ;UAAAjB,QAAA,EAEjBA;QAAQ;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpB,EAAA,CAzQQF,cAAc;EAAA,QACJZ,WAAW,EAGCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EA8MQO,QAAQ;AAAA;AAAAyE,EAAA,GArNrCtE,cAAc;AA2QvB,eAAeA,cAAc;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}