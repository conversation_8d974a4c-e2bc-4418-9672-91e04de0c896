{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { motion } from \"framer-motion\";\nimport { message } from \"antd\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useNavigate } from \"react-router-dom\";\nimport Select from \"react-select\";\nimport { QuizGrid, Card, Button, Input, Loading } from \"../../../components/modern\";\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TbQuestionMark, TbBrain } from \"react-icons/tb\";\nimport { BsBookFill } from \"react-icons/bs\";\nimport \"./style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst primaryClasses = [{\n  value: \"\",\n  label: \"All Classes\"\n}, {\n  value: \"1\",\n  label: \"Class 1\"\n}, {\n  value: \"2\",\n  label: \"Class 2\"\n}, {\n  value: \"3\",\n  label: \"Class 3\"\n}, {\n  value: \"4\",\n  label: \"Class 4\"\n}, {\n  value: \"5\",\n  label: \"Class 5\"\n}, {\n  value: \"6\",\n  label: \"Class 6\"\n}, {\n  value: \"7\",\n  label: \"Class 7\"\n}];\nconst secondaryClasses = [{\n  value: \"\",\n  label: \"All Classes\"\n}, {\n  value: \"Form-1\",\n  label: \"Form 1\"\n}, {\n  value: \"Form-2\",\n  label: \"Form 2\"\n}, {\n  value: \"Form-3\",\n  label: \"Form 3\"\n}, {\n  value: \"Form-4\",\n  label: \"Form 4\"\n}];\nconst advanceClasses = [{\n  value: \"\",\n  label: \"All Classes\"\n}, {\n  value: \"Form-5\",\n  label: \"Form 5\"\n}, {\n  value: \"Form-6\",\n  label: \"Form 6\"\n}];\nfunction Quiz() {\n  _s();\n  var _user$level, _user$level2, _user$name, _user$name$charAt, _user$class, _user$class2;\n  const [exams, setExams] = useState([]);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [reportsData, setReportsData] = useState([]);\n  const [selectedClass, setSelectedClass] = useState(null);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [lgSize, setLgSize] = useState(8);\n  const availableClasses = (user === null || user === void 0 ? void 0 : (_user$level = user.level) === null || _user$level === void 0 ? void 0 : _user$level.toLowerCase()) === \"primary\" ? primaryClasses : (user === null || user === void 0 ? void 0 : (_user$level2 = user.level) === null || _user$level2 === void 0 ? void 0 : _user$level2.toLowerCase()) === \"secondary\" ? secondaryClasses : advanceClasses;\n  useEffect(() => {\n    if (user && user.class) {\n      const defaultSelectedClass = availableClasses.find(option => option.value === user.class);\n      setSelectedClass(defaultSelectedClass);\n    }\n  }, [user, availableClasses]);\n  useEffect(() => {\n    const updateLgSize = () => {\n      setLgSize(window.innerWidth < 1380 ? 9 : 7);\n    };\n\n    // Set initial lg size\n    updateLgSize();\n\n    // Add event listener for window resize\n    window.addEventListener(\"resize\", updateLgSize);\n\n    // Cleanup event listener on component unmount\n    return () => {\n      window.removeEventListener(\"resize\", updateLgSize);\n    };\n  }, []);\n  const handleClassChange = selectedOption => {\n    setSelectedClass(selectedOption);\n  };\n  const filteredExams = exams.filter(exam => {\n    var _exam$name, _exam$category, _exam$class;\n    // Handle class filtering with format compatibility\n    let classMatches = true;\n    if (selectedClass && selectedClass.value !== \"\") {\n      const selectedValue = selectedClass.value;\n      const examClass = exam.class;\n\n      // Check for exact match first\n      if (examClass === selectedValue) {\n        classMatches = true;\n      }\n      // Check if exam class has \"Class-\" prefix and selected value is just the number\n      else if (examClass === `Class-${selectedValue}`) {\n        classMatches = true;\n      }\n      // Check if selected value has \"Class-\" prefix and exam class is just the number\n      else if (selectedValue === `Class-${examClass}`) {\n        classMatches = true;\n      }\n      // Check for Form classes (secondary)\n      else if (examClass === `Form-${selectedValue.replace('Form-', '')}`) {\n        classMatches = true;\n      } else if (selectedValue === `Form-${examClass.replace('Form-', '')}`) {\n        classMatches = true;\n      } else {\n        classMatches = false;\n      }\n    }\n\n    // Handle search filtering\n    const searchMatches = !searchQuery.trim() || ((_exam$name = exam.name) === null || _exam$name === void 0 ? void 0 : _exam$name.toLowerCase().includes(searchQuery.toLowerCase().trim())) || ((_exam$category = exam.category) === null || _exam$category === void 0 ? void 0 : _exam$category.toLowerCase().includes(searchQuery.toLowerCase().trim())) || ((_exam$class = exam.class) === null || _exam$class === void 0 ? void 0 : _exam$class.toLowerCase().includes(searchQuery.toLowerCase().trim()));\n    return classMatches && searchMatches;\n  });\n\n  // Debug logging\n  if (exams.length > 0) {\n    console.log(`📊 Quiz Debug: ${filteredExams.length}/${exams.length} exams shown | Class: ${(selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.label) || 'None'} | Search: \"${searchQuery}\"`);\n  }\n  const getExams = async () => {\n    try {\n      console.log(\"🔍 Starting to fetch exams...\");\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      console.log(\"📡 API Response:\", response);\n      if (response.success) {\n        console.log(\"✅ Exams fetched successfully:\", response.data.length, \"exams\");\n        setExams(response.data.reverse());\n      } else {\n        console.error(\"❌ API Error:\", response.message);\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error(\"❌ Network Error:\", error);\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const filterReportsData = data => {\n    const reportsMap = {};\n\n    // Iterate over the response data (reports)\n    data.forEach(report => {\n      const examId = report.exam._id;\n      const verdict = report.result.verdict;\n\n      // If the examId is not already in the map, add it\n      if (!reportsMap[examId]) {\n        reportsMap[examId] = report;\n      } else {\n        // If there is already an entry for this exam, keep the one with \"pass\" verdict, or just keep the first one if no \"pass\"\n        if (verdict === \"Pass\" && reportsMap[examId].result.verdict !== \"Pass\") {\n          reportsMap[examId] = report; // Replace with the \"pass\" verdict report\n        }\n      }\n    });\n\n    return Object.values(reportsMap);\n  };\n  const getData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      if (response.success) {\n        setReportsData(filterReportsData(response.data));\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    getData();\n    getExams();\n  }, []);\n  const verifyRetake = async exam => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      const retakeCount = response.data.filter(item => item.exam && item.exam._id === exam._id).length;\n      console.log(\"Retake count for exam:\", retakeCount);\n    } catch (error) {\n      message.error(\"Unable to verify retake\");\n      dispatch(HideLoading());\n      return;\n    }\n    dispatch(HideLoading());\n    navigate(`/user/write-exam/${exam._id}`);\n  };\n  const handleSearch = e => {\n    setSearchQuery(e.target.value);\n  };\n  const shouldRenderFilteredExams = filteredExams.length < exams.length;\n  return user && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"heading-2 text-gradient mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"inline w-10 h-10 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), \"Challenge Your Brain, Beat the Rest\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600\",\n            children: \"Test your knowledge and track your progress with our comprehensive quiz system\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6 mb-6 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-primary-600 font-bold text-lg\",\n                  children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"!\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Current Class:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge-primary\",\n                    children: (user === null || user === void 0 ? void 0 : user.level) === \"Primary\" ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : (user === null || user === void 0 ? void 0 : user.level) === \"Secondary\" ? `Form ${user === null || user === void 0 ? void 0 : (_user$class = user.class) === null || _user$class === void 0 ? void 0 : _user$class.replace('Form-', '')}` : (user === null || user === void 0 ? void 0 : user.level) === \"Advance\" ? `Form ${user === null || user === void 0 ? void 0 : (_user$class2 = user.class) === null || _user$class2 === void 0 ? void 0 : _user$class2.replace('Form-', '')}` : (user === null || user === void 0 ? void 0 : user.class) || 'Not Set'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-primary-600\",\n                  children: exams.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Available Quizzes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-success-600\",\n                  children: reportsData.filter(r => {\n                    var _r$result;\n                    return ((_r$result = r.result) === null || _r$result === void 0 ? void 0 : _r$result.verdict) === \"Pass\";\n                  }).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-warning-600\",\n                  children: reportsData.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Total Attempts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row gap-6 items-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Search Quizzes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"Search by quiz title...\",\n                value: searchQuery,\n                onChange: e => handleSearch(e),\n                icon: /*#__PURE__*/_jsxDEV(TbSearch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 27\n                }, this),\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-80\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Filter by Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                options: availableClasses,\n                value: selectedClass,\n                onChange: handleClassChange,\n                placeholder: \"Select Class\",\n                className: \"w-full\",\n                isSearchable: false,\n                styles: {\n                  control: base => ({\n                    ...base,\n                    minHeight: '48px',\n                    borderColor: '#e5e7eb',\n                    '&:hover': {\n                      borderColor: '#3b82f6'\n                    }\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              icon: /*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 25\n              }, this),\n              className: \"lg:w-auto w-full\",\n              children: \"Filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), shouldRenderFilteredExams && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 pt-4 border-t border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Showing \", filteredExams.length, \" of \", exams.length, \" quizzes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: filteredExams.length > 0 ? /*#__PURE__*/_jsxDEV(QuizGrid, {\n          quizzes: filteredExams.map(exam => {\n            var _examReport$result, _examReport$result2, _examReport$result3, _exam$questions, _examReport$result4;\n            const examReport = reportsData.find(report => report.exam && report.exam._id === exam._id);\n            return {\n              ...exam,\n              subject: exam.category,\n              questions: exam.questions || [],\n              duration: exam.duration,\n              difficulty: exam.difficulty || 'Medium',\n              attempts: reportsData.filter(r => {\n                var _r$exam;\n                return ((_r$exam = r.exam) === null || _r$exam === void 0 ? void 0 : _r$exam._id) === exam._id;\n              }).length,\n              userResult: examReport ? {\n                percentage: ((_examReport$result = examReport.result) === null || _examReport$result === void 0 ? void 0 : _examReport$result.percentage) || 0,\n                correctAnswers: ((_examReport$result2 = examReport.result) === null || _examReport$result2 === void 0 ? void 0 : _examReport$result2.correctAnswers) || 0,\n                totalQuestions: ((_examReport$result3 = examReport.result) === null || _examReport$result3 === void 0 ? void 0 : _examReport$result3.totalQuestions) || ((_exam$questions = exam.questions) === null || _exam$questions === void 0 ? void 0 : _exam$questions.length) || 0,\n                verdict: (_examReport$result4 = examReport.result) === null || _examReport$result4 === void 0 ? void 0 : _examReport$result4.verdict,\n                completedAt: examReport.createdAt\n              } : null\n            };\n          }),\n          onQuizStart: quiz => verifyRetake(quiz),\n          onQuizView: quiz => {\n            const examReport = reportsData.find(report => report.exam && report.exam._id === quiz._id);\n            if (examReport) {\n              navigate(`/quiz/${quiz._id}/result`, {\n                state: {\n                  result: examReport.result,\n                  examData: quiz\n                }\n              });\n            }\n          },\n          showResults: true,\n          userResults: reportsData.reduce((acc, report) => {\n            var _report$exam;\n            if ((_report$exam = report.exam) !== null && _report$exam !== void 0 && _report$exam._id) {\n              var _report$result, _report$result2, _report$result2$corre, _report$result3, _report$result4;\n              acc[report.exam._id] = {\n                percentage: ((_report$result = report.result) === null || _report$result === void 0 ? void 0 : _report$result.percentage) || 0,\n                correctAnswers: ((_report$result2 = report.result) === null || _report$result2 === void 0 ? void 0 : (_report$result2$corre = _report$result2.correctAnswers) === null || _report$result2$corre === void 0 ? void 0 : _report$result2$corre.length) || 0,\n                totalQuestions: ((_report$result3 = report.result) === null || _report$result3 === void 0 ? void 0 : _report$result3.totalQuestions) || 0,\n                verdict: (_report$result4 = report.result) === null || _report$result4 === void 0 ? void 0 : _report$result4.verdict,\n                completedAt: report.createdAt\n              };\n            }\n            return acc;\n          }, {})\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-12 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n            className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"No Quizzes Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: searchQuery || selectedClass !== null && selectedClass !== void 0 && selectedClass.value ? \"Try adjusting your search or filter criteria\" : \"No quizzes are available for your current class level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 17\n          }, this), (searchQuery || (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.value)) && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSearchQuery(\"\");\n              setSelectedClass({\n                value: \"\",\n                label: \"All Classes\"\n              });\n            },\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 7\n  }, this);\n}\n_s(Quiz, \"RCSAwxhf6i4RPRBWUpfB/EChHfk=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nexport default Quiz;\nvar _c;\n$RefreshReg$(_c, \"Quiz\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "motion", "message", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "Page<PERSON><PERSON>le", "useNavigate", "Select", "QuizGrid", "Card", "<PERSON><PERSON>", "Input", "Loading", "TbSearch", "Tb<PERSON><PERSON>er", "TbTrophy", "TbClock", "TbUsers", "TbQuestionMark", "TbBrain", "BsBookFill", "jsxDEV", "_jsxDEV", "primaryClasses", "value", "label", "secondaryClasses", "advanceClasses", "Quiz", "_s", "_user$level", "_user$level2", "_user$name", "_user$name$charAt", "_user$class", "_user$class2", "exams", "setExams", "searchQuery", "setSearch<PERSON>uery", "reportsData", "setReportsData", "selectedClass", "setSelectedClass", "navigate", "dispatch", "user", "state", "lgSize", "setLgSize", "availableClasses", "level", "toLowerCase", "class", "defaultSelectedClass", "find", "option", "updateLgSize", "window", "innerWidth", "addEventListener", "removeEventListener", "handleClassChange", "selectedOption", "filteredExams", "filter", "exam", "_exam$name", "_exam$category", "_exam$class", "classMatches", "selected<PERSON><PERSON><PERSON>", "examClass", "replace", "searchMatches", "trim", "name", "includes", "category", "length", "console", "log", "getExams", "response", "success", "data", "reverse", "error", "filterReportsData", "reportsMap", "for<PERSON>ach", "report", "examId", "_id", "verdict", "result", "Object", "values", "getData", "verifyRetake", "retakeCount", "item", "handleSearch", "e", "target", "shouldRenderFilteredExams", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "r", "_r$result", "transition", "delay", "placeholder", "onChange", "icon", "options", "isSearchable", "styles", "control", "base", "minHeight", "borderColor", "variant", "quizzes", "map", "_examReport$result", "_examReport$result2", "_examReport$result3", "_exam$questions", "_examReport$result4", "examReport", "subject", "questions", "duration", "difficulty", "attempts", "_r$exam", "userResult", "percentage", "correctAnswers", "totalQuestions", "completedAt", "createdAt", "onQuizStart", "quiz", "onQuizView", "examData", "showResults", "userResults", "reduce", "acc", "_report$exam", "_report$result", "_report$result2", "_report$result2$corre", "_report$result3", "_report$result4", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { motion } from \"framer-motion\";\r\nimport { message } from \"antd\";\r\nimport { getAllExams } from \"../../../apicalls/exams\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Select from \"react-select\";\r\nimport { QuizGrid, Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport { TbSearch, TbFilter, TbTrophy, TbClock, TbUsers, TbQuestionMark, TbBrain } from \"react-icons/tb\";\r\nimport { BsBookFill } from \"react-icons/bs\";\r\nimport \"./style.css\";\r\n\r\n\r\nconst primaryClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"1\", label: \"Class 1\" },\r\n  { value: \"2\", label: \"Class 2\" },\r\n  { value: \"3\", label: \"Class 3\" },\r\n  { value: \"4\", label: \"Class 4\" },\r\n  { value: \"5\", label: \"Class 5\" },\r\n  { value: \"6\", label: \"Class 6\" },\r\n  { value: \"7\", label: \"Class 7\" },\r\n];\r\n\r\nconst secondaryClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"Form-1\", label: \"Form 1\" },\r\n  { value: \"Form-2\", label: \"Form 2\" },\r\n  { value: \"Form-3\", label: \"Form 3\" },\r\n  { value: \"Form-4\", label: \"Form 4\" },\r\n];\r\n\r\nconst advanceClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"Form-5\", label: \"Form 5\" },\r\n  { value: \"Form-6\", label: \"Form 6\" },\r\n];\r\n\r\nfunction Quiz() {\r\n  const [exams, setExams] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [selectedClass, setSelectedClass] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n  const [lgSize, setLgSize] = useState(8);\r\n\r\n  const availableClasses =\r\n    user?.level?.toLowerCase() === \"primary\"\r\n      ? primaryClasses\r\n      : user?.level?.toLowerCase() === \"secondary\"\r\n        ? secondaryClasses\r\n        : advanceClasses;\r\n\r\n  useEffect(() => {\r\n    if (user && user.class) {\r\n      const defaultSelectedClass = availableClasses.find(\r\n        (option) => option.value === user.class\r\n      );\r\n      setSelectedClass(defaultSelectedClass);\r\n    }\r\n  }, [user, availableClasses]);\r\n\r\n  useEffect(() => {\r\n    const updateLgSize = () => {\r\n      setLgSize(window.innerWidth < 1380 ? 9 : 7);\r\n    };\r\n\r\n    // Set initial lg size\r\n    updateLgSize();\r\n\r\n    // Add event listener for window resize\r\n    window.addEventListener(\"resize\", updateLgSize);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener(\"resize\", updateLgSize);\r\n    };\r\n  }, []);\r\n\r\n  const handleClassChange = (selectedOption) => {\r\n    setSelectedClass(selectedOption);\r\n  };\r\n\r\n  const filteredExams = exams.filter(\r\n    (exam) => {\r\n      // Handle class filtering with format compatibility\r\n      let classMatches = true;\r\n      if (selectedClass && selectedClass.value !== \"\") {\r\n        const selectedValue = selectedClass.value;\r\n        const examClass = exam.class;\r\n\r\n        // Check for exact match first\r\n        if (examClass === selectedValue) {\r\n          classMatches = true;\r\n        }\r\n        // Check if exam class has \"Class-\" prefix and selected value is just the number\r\n        else if (examClass === `Class-${selectedValue}`) {\r\n          classMatches = true;\r\n        }\r\n        // Check if selected value has \"Class-\" prefix and exam class is just the number\r\n        else if (selectedValue === `Class-${examClass}`) {\r\n          classMatches = true;\r\n        }\r\n        // Check for Form classes (secondary)\r\n        else if (examClass === `Form-${selectedValue.replace('Form-', '')}`) {\r\n          classMatches = true;\r\n        }\r\n        else if (selectedValue === `Form-${examClass.replace('Form-', '')}`) {\r\n          classMatches = true;\r\n        }\r\n        else {\r\n          classMatches = false;\r\n        }\r\n      }\r\n\r\n      // Handle search filtering\r\n      const searchMatches = !searchQuery.trim() ||\r\n        exam.name?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||\r\n        exam.category?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||\r\n        exam.class?.toLowerCase().includes(searchQuery.toLowerCase().trim());\r\n\r\n      return classMatches && searchMatches;\r\n    }\r\n  );\r\n\r\n  // Debug logging\r\n  if (exams.length > 0) {\r\n    console.log(`📊 Quiz Debug: ${filteredExams.length}/${exams.length} exams shown | Class: ${selectedClass?.label || 'None'} | Search: \"${searchQuery}\"`);\r\n  }\r\n\r\n  const getExams = async () => {\r\n    try {\r\n      console.log(\"🔍 Starting to fetch exams...\");\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      console.log(\"📡 API Response:\", response);\r\n      if (response.success) {\r\n        console.log(\"✅ Exams fetched successfully:\", response.data.length, \"exams\");\r\n        setExams(response.data.reverse());\r\n      } else {\r\n        console.error(\"❌ API Error:\", response.message);\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      console.error(\"❌ Network Error:\", error);\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const filterReportsData = (data) => {\r\n    const reportsMap = {};\r\n\r\n    // Iterate over the response data (reports)\r\n    data.forEach(report => {\r\n      const examId = report.exam._id;\r\n      const verdict = report.result.verdict;\r\n\r\n      // If the examId is not already in the map, add it\r\n      if (!reportsMap[examId]) {\r\n        reportsMap[examId] = report;\r\n      } else {\r\n        // If there is already an entry for this exam, keep the one with \"pass\" verdict, or just keep the first one if no \"pass\"\r\n        if (verdict === \"Pass\" && reportsMap[examId].result.verdict !== \"Pass\") {\r\n          reportsMap[examId] = report; // Replace with the \"pass\" verdict report\r\n        }\r\n      }\r\n    });\r\n\r\n    return Object.values(reportsMap);\r\n  };\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n\r\n        setReportsData(filterReportsData(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n    getExams();\r\n  }, []);\r\n\r\n  const verifyRetake = async (exam) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      const retakeCount = response.data.filter(\r\n        (item) => item.exam && item.exam._id === exam._id\r\n      ).length;\r\n      console.log(\"Retake count for exam:\", retakeCount);\r\n    } catch (error) {\r\n      message.error(\"Unable to verify retake\");\r\n      dispatch(HideLoading());\r\n      return;\r\n    }\r\n    dispatch(HideLoading());\r\n    navigate(`/user/write-exam/${exam._id}`);\r\n  };\r\n\r\n  const handleSearch = (e) => {\r\n    setSearchQuery(e.target.value);\r\n  };\r\n\r\n  const shouldRenderFilteredExams = filteredExams.length < exams.length;\r\n\r\n  return (\r\n    user && (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6\">\r\n        <div className=\"container-modern\">\r\n          {/* Modern Header */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            className=\"mb-8\"\r\n          >\r\n            <div className=\"text-center mb-6\">\r\n              <h1 className=\"heading-2 text-gradient mb-4\">\r\n                <TbBrain className=\"inline w-10 h-10 mr-3\" />\r\n                Challenge Your Brain, Beat the Rest\r\n              </h1>\r\n              <p className=\"text-xl text-gray-600\">\r\n                Test your knowledge and track your progress with our comprehensive quiz system\r\n              </p>\r\n            </div>\r\n\r\n            {/* User Info Card */}\r\n            <Card className=\"p-6 mb-6 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-primary-600 font-bold text-lg\">\r\n                      {user?.name?.charAt(0)?.toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-gray-900\">Welcome back, {user?.name}!</h3>\r\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\r\n                      <span>Current Class:</span>\r\n                      <span className=\"badge-primary\">\r\n                        {user?.level === \"Primary\"\r\n                          ? `Class ${user?.class}`\r\n                          : user?.level === \"Secondary\"\r\n                          ? `Form ${user?.class?.replace('Form-', '')}`\r\n                          : user?.level === \"Advance\"\r\n                          ? `Form ${user?.class?.replace('Form-', '')}`\r\n                          : user?.class || 'Not Set'}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Quick Stats */}\r\n                <div className=\"hidden md:flex items-center space-x-6\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-2xl font-bold text-primary-600\">{exams.length}</div>\r\n                    <div className=\"text-xs text-gray-500\">Available Quizzes</div>\r\n                  </div>\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-2xl font-bold text-success-600\">{reportsData.filter(r => r.result?.verdict === \"Pass\").length}</div>\r\n                    <div className=\"text-xs text-gray-500\">Passed</div>\r\n                  </div>\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-2xl font-bold text-warning-600\">{reportsData.length}</div>\r\n                    <div className=\"text-xs text-gray-500\">Total Attempts</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Card>\r\n          </motion.div>\r\n\r\n\r\n          {/* Modern Filters */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.2 }}\r\n            className=\"mb-8\"\r\n          >\r\n            <Card className=\"p-6\">\r\n              <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\r\n                {/* Search */}\r\n                <div className=\"flex-1\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Search Quizzes\r\n                  </label>\r\n                  <Input\r\n                    placeholder=\"Search by quiz title...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => handleSearch(e)}\r\n                    icon={<TbSearch />}\r\n                    className=\"w-full\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Class Filter */}\r\n                <div className=\"w-full lg:w-80\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Filter by Class\r\n                  </label>\r\n                  <Select\r\n                    options={availableClasses}\r\n                    value={selectedClass}\r\n                    onChange={handleClassChange}\r\n                    placeholder=\"Select Class\"\r\n                    className=\"w-full\"\r\n                    isSearchable={false}\r\n                    styles={{\r\n                      control: (base) => ({\r\n                        ...base,\r\n                        minHeight: '48px',\r\n                        borderColor: '#e5e7eb',\r\n                        '&:hover': { borderColor: '#3b82f6' },\r\n                      }),\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Filter Button */}\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  icon={<TbFilter />}\r\n                  className=\"lg:w-auto w-full\"\r\n                >\r\n                  Filter\r\n                </Button>\r\n              </div>\r\n\r\n              {/* Results Count */}\r\n              {shouldRenderFilteredExams && (\r\n                <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n                  <span className=\"text-sm text-gray-600\">\r\n                    Showing {filteredExams.length} of {exams.length} quizzes\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </Card>\r\n          </motion.div>\r\n\r\n          {/* Modern Quiz Grid */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4 }}\r\n          >\r\n            {filteredExams.length > 0 ? (\r\n              <QuizGrid\r\n                quizzes={filteredExams.map(exam => {\r\n                  const examReport = reportsData.find(\r\n                    (report) => report.exam && report.exam._id === exam._id\r\n                  );\r\n\r\n                  return {\r\n                    ...exam,\r\n                    subject: exam.category,\r\n                    questions: exam.questions || [],\r\n                    duration: exam.duration,\r\n                    difficulty: exam.difficulty || 'Medium',\r\n                    attempts: reportsData.filter(r => r.exam?._id === exam._id).length,\r\n                    userResult: examReport ? {\r\n                      percentage: examReport.result?.percentage || 0,\r\n                      correctAnswers: examReport.result?.correctAnswers || 0,\r\n                      totalQuestions: examReport.result?.totalQuestions || exam.questions?.length || 0,\r\n                      verdict: examReport.result?.verdict,\r\n                      completedAt: examReport.createdAt\r\n                    } : null\r\n                  };\r\n                })}\r\n                onQuizStart={(quiz) => verifyRetake(quiz)}\r\n                onQuizView={(quiz) => {\r\n                  const examReport = reportsData.find(\r\n                    (report) => report.exam && report.exam._id === quiz._id\r\n                  );\r\n                  if (examReport) {\r\n                    navigate(`/quiz/${quiz._id}/result`, {\r\n                      state: { result: examReport.result, examData: quiz }\r\n                    });\r\n                  }\r\n                }}\r\n                showResults={true}\r\n                userResults={reportsData.reduce((acc, report) => {\r\n                  if (report.exam?._id) {\r\n                    acc[report.exam._id] = {\r\n                      percentage: report.result?.percentage || 0,\r\n                      correctAnswers: report.result?.correctAnswers?.length || 0,\r\n                      totalQuestions: report.result?.totalQuestions || 0,\r\n                      verdict: report.result?.verdict,\r\n                      completedAt: report.createdAt\r\n                    };\r\n                  }\r\n                  return acc;\r\n                }, {})}\r\n              />\r\n            ) : (\r\n              <Card className=\"p-12 text-center\">\r\n                <TbQuestionMark className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 mb-6\">\r\n                  {searchQuery || selectedClass?.value\r\n                    ? \"Try adjusting your search or filter criteria\"\r\n                    : \"No quizzes are available for your current class level\"}\r\n                </p>\r\n                {(searchQuery || selectedClass?.value) && (\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    onClick={() => {\r\n                      setSearchQuery(\"\");\r\n                      setSelectedClass({ value: \"\", label: \"All Classes\" });\r\n                    }}\r\n                  >\r\n                    Clear Filters\r\n                  </Button>\r\n                )}\r\n              </Card>\r\n            )}\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    )\r\n  );\r\n}\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,4BAA4B;AACnF,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAEC,OAAO,QAAQ,gBAAgB;AACxG,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGrB,MAAMC,cAAc,GAAG,CACrB;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAc,CAAC,EACnC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,CACjC;AAED,MAAMC,gBAAgB,GAAG,CACvB;EAAEF,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAc,CAAC,EACnC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,CACrC;AAED,MAAME,cAAc,GAAG,CACrB;EAAEH,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAc,CAAC,EACnC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,CACrC;AAED,SAASG,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,UAAA,EAAAC,iBAAA,EAAAC,WAAA,EAAAC,YAAA;EACd,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMiD,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiD;EAAK,CAAC,GAAGhD,WAAW,CAAEiD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EAEvC,MAAMuD,gBAAgB,GACpB,CAAAJ,IAAI,aAAJA,IAAI,wBAAAhB,WAAA,GAAJgB,IAAI,CAAEK,KAAK,cAAArB,WAAA,uBAAXA,WAAA,CAAasB,WAAW,CAAC,CAAC,MAAK,SAAS,GACpC7B,cAAc,GACd,CAAAuB,IAAI,aAAJA,IAAI,wBAAAf,YAAA,GAAJe,IAAI,CAAEK,KAAK,cAAApB,YAAA,uBAAXA,YAAA,CAAaqB,WAAW,CAAC,CAAC,MAAK,WAAW,GACxC1B,gBAAgB,GAChBC,cAAc;EAEtB/B,SAAS,CAAC,MAAM;IACd,IAAIkD,IAAI,IAAIA,IAAI,CAACO,KAAK,EAAE;MACtB,MAAMC,oBAAoB,GAAGJ,gBAAgB,CAACK,IAAI,CAC/CC,MAAM,IAAKA,MAAM,CAAChC,KAAK,KAAKsB,IAAI,CAACO,KACpC,CAAC;MACDV,gBAAgB,CAACW,oBAAoB,CAAC;IACxC;EACF,CAAC,EAAE,CAACR,IAAI,EAAEI,gBAAgB,CAAC,CAAC;EAE5BtD,SAAS,CAAC,MAAM;IACd,MAAM6D,YAAY,GAAGA,CAAA,KAAM;MACzBR,SAAS,CAACS,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;;IAED;IACAF,YAAY,CAAC,CAAC;;IAEd;IACAC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;;IAE/C;IACA,OAAO,MAAM;MACXC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,iBAAiB,GAAIC,cAAc,IAAK;IAC5CpB,gBAAgB,CAACoB,cAAc,CAAC;EAClC,CAAC;EAED,MAAMC,aAAa,GAAG5B,KAAK,CAAC6B,MAAM,CAC/BC,IAAI,IAAK;IAAA,IAAAC,UAAA,EAAAC,cAAA,EAAAC,WAAA;IACR;IACA,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAI5B,aAAa,IAAIA,aAAa,CAAClB,KAAK,KAAK,EAAE,EAAE;MAC/C,MAAM+C,aAAa,GAAG7B,aAAa,CAAClB,KAAK;MACzC,MAAMgD,SAAS,GAAGN,IAAI,CAACb,KAAK;;MAE5B;MACA,IAAImB,SAAS,KAAKD,aAAa,EAAE;QAC/BD,YAAY,GAAG,IAAI;MACrB;MACA;MAAA,KACK,IAAIE,SAAS,KAAM,SAAQD,aAAc,EAAC,EAAE;QAC/CD,YAAY,GAAG,IAAI;MACrB;MACA;MAAA,KACK,IAAIC,aAAa,KAAM,SAAQC,SAAU,EAAC,EAAE;QAC/CF,YAAY,GAAG,IAAI;MACrB;MACA;MAAA,KACK,IAAIE,SAAS,KAAM,QAAOD,aAAa,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,EAAE;QACnEH,YAAY,GAAG,IAAI;MACrB,CAAC,MACI,IAAIC,aAAa,KAAM,QAAOC,SAAS,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,EAAE;QACnEH,YAAY,GAAG,IAAI;MACrB,CAAC,MACI;QACHA,YAAY,GAAG,KAAK;MACtB;IACF;;IAEA;IACA,MAAMI,aAAa,GAAG,CAACpC,WAAW,CAACqC,IAAI,CAAC,CAAC,MAAAR,UAAA,GACvCD,IAAI,CAACU,IAAI,cAAAT,UAAA,uBAATA,UAAA,CAAWf,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACvC,WAAW,CAACc,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAC,OAAAP,cAAA,GACnEF,IAAI,CAACY,QAAQ,cAAAV,cAAA,uBAAbA,cAAA,CAAehB,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACvC,WAAW,CAACc,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAC,OAAAN,WAAA,GACvEH,IAAI,CAACb,KAAK,cAAAgB,WAAA,uBAAVA,WAAA,CAAYjB,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACvC,WAAW,CAACc,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAC;IAEtE,OAAOL,YAAY,IAAII,aAAa;EACtC,CACF,CAAC;;EAED;EACA,IAAItC,KAAK,CAAC2C,MAAM,GAAG,CAAC,EAAE;IACpBC,OAAO,CAACC,GAAG,CAAE,kBAAiBjB,aAAa,CAACe,MAAO,IAAG3C,KAAK,CAAC2C,MAAO,yBAAwB,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEjB,KAAK,KAAI,MAAO,eAAca,WAAY,GAAE,CAAC;EACzJ;EAEA,MAAM4C,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFF,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CpC,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+E,QAAQ,GAAG,MAAMlF,WAAW,CAAC,CAAC;MACpC+E,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,QAAQ,CAAC;MACzC,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEE,QAAQ,CAACE,IAAI,CAACN,MAAM,EAAE,OAAO,CAAC;QAC3E1C,QAAQ,CAAC8C,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLN,OAAO,CAACO,KAAK,CAAC,cAAc,EAAEJ,QAAQ,CAACnF,OAAO,CAAC;QAC/CA,OAAO,CAACuF,KAAK,CAACJ,QAAQ,CAACnF,OAAO,CAAC;MACjC;MACA6C,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOoF,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC1C,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACuF,KAAK,CAACA,KAAK,CAACvF,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMwF,iBAAiB,GAAIH,IAAI,IAAK;IAClC,MAAMI,UAAU,GAAG,CAAC,CAAC;;IAErB;IACAJ,IAAI,CAACK,OAAO,CAACC,MAAM,IAAI;MACrB,MAAMC,MAAM,GAAGD,MAAM,CAACzB,IAAI,CAAC2B,GAAG;MAC9B,MAAMC,OAAO,GAAGH,MAAM,CAACI,MAAM,CAACD,OAAO;;MAErC;MACA,IAAI,CAACL,UAAU,CAACG,MAAM,CAAC,EAAE;QACvBH,UAAU,CAACG,MAAM,CAAC,GAAGD,MAAM;MAC7B,CAAC,MAAM;QACL;QACA,IAAIG,OAAO,KAAK,MAAM,IAAIL,UAAU,CAACG,MAAM,CAAC,CAACG,MAAM,CAACD,OAAO,KAAK,MAAM,EAAE;UACtEL,UAAU,CAACG,MAAM,CAAC,GAAGD,MAAM,CAAC,CAAC;QAC/B;MACF;IACF,CAAC,CAAC;;IAEF,OAAOK,MAAM,CAACC,MAAM,CAACR,UAAU,CAAC;EAClC,CAAC;EAED,MAAMS,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACFrD,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+E,QAAQ,GAAG,MAAMjF,mBAAmB,CAAC,CAAC;MAC5C,IAAIiF,QAAQ,CAACC,OAAO,EAAE;QAEpB3C,cAAc,CAAC+C,iBAAiB,CAACL,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClD,CAAC,MAAM;QACLrF,OAAO,CAACuF,KAAK,CAACJ,QAAQ,CAACnF,OAAO,CAAC;MACjC;MACA6C,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOoF,KAAK,EAAE;MACd1C,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACuF,KAAK,CAACA,KAAK,CAACvF,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDJ,SAAS,CAAC,MAAM;IACdsG,OAAO,CAAC,CAAC;IACThB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,YAAY,GAAG,MAAOjC,IAAI,IAAK;IACnC,IAAI;MACFrB,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+E,QAAQ,GAAG,MAAMjF,mBAAmB,CAAC,CAAC;MAC5C,MAAMkG,WAAW,GAAGjB,QAAQ,CAACE,IAAI,CAACpB,MAAM,CACrCoC,IAAI,IAAKA,IAAI,CAACnC,IAAI,IAAImC,IAAI,CAACnC,IAAI,CAAC2B,GAAG,KAAK3B,IAAI,CAAC2B,GAChD,CAAC,CAACd,MAAM;MACRC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,WAAW,CAAC;IACpD,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdvF,OAAO,CAACuF,KAAK,CAAC,yBAAyB,CAAC;MACxC1C,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;MACvB;IACF;IACA0C,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;IACvByC,QAAQ,CAAE,oBAAmBsB,IAAI,CAAC2B,GAAI,EAAC,CAAC;EAC1C,CAAC;EAED,MAAMS,YAAY,GAAIC,CAAC,IAAK;IAC1BhE,cAAc,CAACgE,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAC;EAChC,CAAC;EAED,MAAMiF,yBAAyB,GAAGzC,aAAa,CAACe,MAAM,GAAG3C,KAAK,CAAC2C,MAAM;EAErE,OACEjC,IAAI,iBACFxB,OAAA;IAAKoF,SAAS,EAAC,4DAA4D;IAAAC,QAAA,eACzErF,OAAA;MAAKoF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BrF,OAAA,CAACvB,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAEhBrF,OAAA;UAAKoF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrF,OAAA;YAAIoF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC1CrF,OAAA,CAACH,OAAO;cAACuF,SAAS,EAAC;YAAuB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uCAE/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9F,OAAA;YAAGoF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN9F,OAAA,CAACb,IAAI;UAACiG,SAAS,EAAC,yEAAyE;UAAAC,QAAA,eACvFrF,OAAA;YAAKoF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrF,OAAA;cAAKoF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CrF,OAAA;gBAAKoF,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrFrF,OAAA;kBAAMoF,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EACjD7D,IAAI,aAAJA,IAAI,wBAAAd,UAAA,GAAJc,IAAI,CAAE8B,IAAI,cAAA5C,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYqF,MAAM,CAAC,CAAC,CAAC,cAAApF,iBAAA,uBAArBA,iBAAA,CAAuBqF,WAAW,CAAC;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN9F,OAAA;gBAAAqF,QAAA,gBACErF,OAAA;kBAAIoF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,GAAC,gBAAc,EAAC7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,EAAC,GAAC;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpF9F,OAAA;kBAAKoF,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAChErF,OAAA;oBAAAqF,QAAA,EAAM;kBAAc;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3B9F,OAAA;oBAAMoF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC5B,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,MAAK,SAAS,GACrB,SAAQL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAM,EAAC,GACtB,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,MAAK,WAAW,GAC1B,QAAOL,IAAI,aAAJA,IAAI,wBAAAZ,WAAA,GAAJY,IAAI,CAAEO,KAAK,cAAAnB,WAAA,uBAAXA,WAAA,CAAauC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,GAC3C,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,MAAK,SAAS,GACxB,QAAOL,IAAI,aAAJA,IAAI,wBAAAX,YAAA,GAAJW,IAAI,CAAEO,KAAK,cAAAlB,YAAA,uBAAXA,YAAA,CAAasC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,GAC3C,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI;kBAAS;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9F,OAAA;cAAKoF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrF,OAAA;gBAAKoF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrF,OAAA;kBAAKoF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAEvE,KAAK,CAAC2C;gBAAM;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzE9F,OAAA;kBAAKoF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACN9F,OAAA;gBAAKoF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrF,OAAA;kBAAKoF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAEnE,WAAW,CAACyB,MAAM,CAACsD,CAAC;oBAAA,IAAAC,SAAA;oBAAA,OAAI,EAAAA,SAAA,GAAAD,CAAC,CAACxB,MAAM,cAAAyB,SAAA,uBAARA,SAAA,CAAU1B,OAAO,MAAK,MAAM;kBAAA,EAAC,CAACf;gBAAM;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzH9F,OAAA;kBAAKoF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN9F,OAAA;gBAAKoF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrF,OAAA;kBAAKoF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAEnE,WAAW,CAACuC;gBAAM;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/E9F,OAAA;kBAAKoF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAIb9F,OAAA,CAACvB,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BU,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BhB,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhBrF,OAAA,CAACb,IAAI;UAACiG,SAAS,EAAC,KAAK;UAAAC,QAAA,gBACnBrF,OAAA;YAAKoF,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBAExDrF,OAAA;cAAKoF,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBrF,OAAA;gBAAOoF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9F,OAAA,CAACX,KAAK;gBACJgH,WAAW,EAAC,yBAAyB;gBACrCnG,KAAK,EAAEc,WAAY;gBACnBsF,QAAQ,EAAGrB,CAAC,IAAKD,YAAY,CAACC,CAAC,CAAE;gBACjCsB,IAAI,eAAEvG,OAAA,CAACT,QAAQ;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBV,SAAS,EAAC;cAAQ;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN9F,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrF,OAAA;gBAAOoF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9F,OAAA,CAACf,MAAM;gBACLuH,OAAO,EAAE5E,gBAAiB;gBAC1B1B,KAAK,EAAEkB,aAAc;gBACrBkF,QAAQ,EAAE9D,iBAAkB;gBAC5B6D,WAAW,EAAC,cAAc;gBAC1BjB,SAAS,EAAC,QAAQ;gBAClBqB,YAAY,EAAE,KAAM;gBACpBC,MAAM,EAAE;kBACNC,OAAO,EAAGC,IAAI,KAAM;oBAClB,GAAGA,IAAI;oBACPC,SAAS,EAAE,MAAM;oBACjBC,WAAW,EAAE,SAAS;oBACtB,SAAS,EAAE;sBAAEA,WAAW,EAAE;oBAAU;kBACtC,CAAC;gBACH;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN9F,OAAA,CAACZ,MAAM;cACL2H,OAAO,EAAC,WAAW;cACnBR,IAAI,eAAEvG,OAAA,CAACR,QAAQ;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBV,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC7B;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLX,yBAAyB,iBACxBnF,OAAA;YAAKoF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjDrF,OAAA;cAAMoF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,UAC9B,EAAC3C,aAAa,CAACe,MAAM,EAAC,MAAI,EAAC3C,KAAK,CAAC2C,MAAM,EAAC,UAClD;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGb9F,OAAA,CAACvB,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BU,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAf,QAAA,EAE1B3C,aAAa,CAACe,MAAM,GAAG,CAAC,gBACvBzD,OAAA,CAACd,QAAQ;UACP8H,OAAO,EAAEtE,aAAa,CAACuE,GAAG,CAACrE,IAAI,IAAI;YAAA,IAAAsE,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,eAAA,EAAAC,mBAAA;YACjC,MAAMC,UAAU,GAAGrG,WAAW,CAACe,IAAI,CAChCoC,MAAM,IAAKA,MAAM,CAACzB,IAAI,IAAIyB,MAAM,CAACzB,IAAI,CAAC2B,GAAG,KAAK3B,IAAI,CAAC2B,GACtD,CAAC;YAED,OAAO;cACL,GAAG3B,IAAI;cACP4E,OAAO,EAAE5E,IAAI,CAACY,QAAQ;cACtBiE,SAAS,EAAE7E,IAAI,CAAC6E,SAAS,IAAI,EAAE;cAC/BC,QAAQ,EAAE9E,IAAI,CAAC8E,QAAQ;cACvBC,UAAU,EAAE/E,IAAI,CAAC+E,UAAU,IAAI,QAAQ;cACvCC,QAAQ,EAAE1G,WAAW,CAACyB,MAAM,CAACsD,CAAC;gBAAA,IAAA4B,OAAA;gBAAA,OAAI,EAAAA,OAAA,GAAA5B,CAAC,CAACrD,IAAI,cAAAiF,OAAA,uBAANA,OAAA,CAAQtD,GAAG,MAAK3B,IAAI,CAAC2B,GAAG;cAAA,EAAC,CAACd,MAAM;cAClEqE,UAAU,EAAEP,UAAU,GAAG;gBACvBQ,UAAU,EAAE,EAAAb,kBAAA,GAAAK,UAAU,CAAC9C,MAAM,cAAAyC,kBAAA,uBAAjBA,kBAAA,CAAmBa,UAAU,KAAI,CAAC;gBAC9CC,cAAc,EAAE,EAAAb,mBAAA,GAAAI,UAAU,CAAC9C,MAAM,cAAA0C,mBAAA,uBAAjBA,mBAAA,CAAmBa,cAAc,KAAI,CAAC;gBACtDC,cAAc,EAAE,EAAAb,mBAAA,GAAAG,UAAU,CAAC9C,MAAM,cAAA2C,mBAAA,uBAAjBA,mBAAA,CAAmBa,cAAc,OAAAZ,eAAA,GAAIzE,IAAI,CAAC6E,SAAS,cAAAJ,eAAA,uBAAdA,eAAA,CAAgB5D,MAAM,KAAI,CAAC;gBAChFe,OAAO,GAAA8C,mBAAA,GAAEC,UAAU,CAAC9C,MAAM,cAAA6C,mBAAA,uBAAjBA,mBAAA,CAAmB9C,OAAO;gBACnC0D,WAAW,EAAEX,UAAU,CAACY;cAC1B,CAAC,GAAG;YACN,CAAC;UACH,CAAC,CAAE;UACHC,WAAW,EAAGC,IAAI,IAAKxD,YAAY,CAACwD,IAAI,CAAE;UAC1CC,UAAU,EAAGD,IAAI,IAAK;YACpB,MAAMd,UAAU,GAAGrG,WAAW,CAACe,IAAI,CAChCoC,MAAM,IAAKA,MAAM,CAACzB,IAAI,IAAIyB,MAAM,CAACzB,IAAI,CAAC2B,GAAG,KAAK8D,IAAI,CAAC9D,GACtD,CAAC;YACD,IAAIgD,UAAU,EAAE;cACdjG,QAAQ,CAAE,SAAQ+G,IAAI,CAAC9D,GAAI,SAAQ,EAAE;gBACnC9C,KAAK,EAAE;kBAAEgD,MAAM,EAAE8C,UAAU,CAAC9C,MAAM;kBAAE8D,QAAQ,EAAEF;gBAAK;cACrD,CAAC,CAAC;YACJ;UACF,CAAE;UACFG,WAAW,EAAE,IAAK;UAClBC,WAAW,EAAEvH,WAAW,CAACwH,MAAM,CAAC,CAACC,GAAG,EAAEtE,MAAM,KAAK;YAAA,IAAAuE,YAAA;YAC/C,KAAAA,YAAA,GAAIvE,MAAM,CAACzB,IAAI,cAAAgG,YAAA,eAAXA,YAAA,CAAarE,GAAG,EAAE;cAAA,IAAAsE,cAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,eAAA;cACpBN,GAAG,CAACtE,MAAM,CAACzB,IAAI,CAAC2B,GAAG,CAAC,GAAG;gBACrBwD,UAAU,EAAE,EAAAc,cAAA,GAAAxE,MAAM,CAACI,MAAM,cAAAoE,cAAA,uBAAbA,cAAA,CAAed,UAAU,KAAI,CAAC;gBAC1CC,cAAc,EAAE,EAAAc,eAAA,GAAAzE,MAAM,CAACI,MAAM,cAAAqE,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAed,cAAc,cAAAe,qBAAA,uBAA7BA,qBAAA,CAA+BtF,MAAM,KAAI,CAAC;gBAC1DwE,cAAc,EAAE,EAAAe,eAAA,GAAA3E,MAAM,CAACI,MAAM,cAAAuE,eAAA,uBAAbA,eAAA,CAAef,cAAc,KAAI,CAAC;gBAClDzD,OAAO,GAAAyE,eAAA,GAAE5E,MAAM,CAACI,MAAM,cAAAwE,eAAA,uBAAbA,eAAA,CAAezE,OAAO;gBAC/B0D,WAAW,EAAE7D,MAAM,CAAC8D;cACtB,CAAC;YACH;YACA,OAAOQ,GAAG;UACZ,CAAC,EAAE,CAAC,CAAC;QAAE;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,gBAEF9F,OAAA,CAACb,IAAI;UAACiG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAChCrF,OAAA,CAACJ,cAAc;YAACwF,SAAS,EAAC;UAAsC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnE9F,OAAA;YAAIoF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAgB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9E9F,OAAA;YAAGoF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAC9BrE,WAAW,IAAII,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAElB,KAAK,GAChC,8CAA8C,GAC9C;UAAuD;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,EACH,CAAC9E,WAAW,KAAII,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAElB,KAAK,mBACnCF,OAAA,CAACZ,MAAM;YACL2H,OAAO,EAAC,WAAW;YACnBmC,OAAO,EAAEA,CAAA,KAAM;cACbjI,cAAc,CAAC,EAAE,CAAC;cAClBI,gBAAgB,CAAC;gBAAEnB,KAAK,EAAE,EAAE;gBAAEC,KAAK,EAAE;cAAc,CAAC,CAAC;YACvD,CAAE;YAAAkF,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;AAEL;AAACvF,EAAA,CA5YQD,IAAI;EAAA,QAKMtB,WAAW,EACXT,WAAW,EACXC,WAAW;AAAA;AAAA2K,EAAA,GAPrB7I,IAAI;AA8Yb,eAAeA,IAAI;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}