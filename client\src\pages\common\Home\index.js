import React, { useState, useEffect, useRef } from "react";
import "./index.css";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  TbArrowBigRightLinesFilled,
  TbBrain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbStar,
  TbSchool,
  TbMenu2,
  TbX,
  TbMoon,
  TbSun
} from "react-icons/tb";
import { Rate, message } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllReviews } from "../../../apicalls/reviews";
import Image1 from "../../../assets/collage-1.png";
import Image2 from "../../../assets/collage-2.png";
import { contactUs } from "../../../apicalls/users";
import { useTheme } from "../../../contexts/ThemeContext";
import { Button } from "../../../components/modern";

const Home = () => {
  const homeSectionRef = useRef(null);
  const aboutUsSectionRef = useRef(null);
  const reviewsSectionRef = useRef(null);
  const contactUsRef = useRef(null);
  const [reviews, setReviews] = useState([]);
  const dispatch = useDispatch();
  const [menuOpen, setMenuOpen] = useState(false);
  const [formData, setFormData] = useState({ name: "", email: "", message: "" });
  const [loading, setLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const { isDarkMode, toggleTheme } = useTheme();

  useEffect(() => { getReviews(); }, []);

  const getReviews = async () => {
    dispatch(ShowLoading());
    try {
      const response = await getAllReviews();
      if (response.success) {
        setReviews(response.data);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
    dispatch(HideLoading());
  };

  const scrollToSection = (ref, offset = 80) => {
    if (ref?.current) {
      const sectionTop = ref.current.offsetTop;
      window.scrollTo({ top: sectionTop - offset, behavior: "smooth" });
      // Close mobile menu after clicking
      setMenuOpen(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponseMessage("");
    try {
      const data = await contactUs(formData);
      if (data.success) {
        message.success("Message sent successfully!");
        setResponseMessage("Message sent successfully!");
        setFormData({ name: "", email: "", message: "" });
      } else {
        setResponseMessage(data.message || "Something went wrong.");
      }
    } catch (error) {
      setResponseMessage("Error sending message. Please try again.");
    }
    setLoading(false);
  };

  return (
    <div className="Home">
      {/* Navigation */}
      <motion.nav
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="nav-header fixed w-full top-0 z-50"
      >
        <div className="container">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <TbBrain className="w-8 h-8" style={{color: '#007BFF'}} />
              <span className="logo-text">
                Brain<span className="logo-accent">Wave</span>
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <button onClick={() => scrollToSection(homeSectionRef)} className="nav-item">Home</button>
              <button onClick={() => scrollToSection(aboutUsSectionRef)} className="nav-item">About Us</button>
              <button onClick={() => scrollToSection(reviewsSectionRef)} className="nav-item">Reviews</button>
              <button onClick={() => scrollToSection(contactUsRef)} className="nav-item">Contact Us</button>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-4">
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
              >
                {isDarkMode ? <TbSun className="w-5 h-5" /> : <TbMoon className="w-5 h-5" />}
              </button>

              {/* Auth Buttons - Desktop */}
              <div className="hidden md:flex items-center space-x-3">
                <Link to="/login">
                  <button className="btn btn-secondary">Login</button>
                </Link>
                <Link to="/register">
                  <button className="btn btn-primary">Sign Up</button>
                </Link>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setMenuOpen(!menuOpen)}
                className="md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
              >
                {menuOpen ? <TbX className="w-6 h-6" /> : <TbMenu2 className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {menuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="md:hidden mobile-nav"
            >
              <div className="flex flex-col">
                <button onClick={() => scrollToSection(homeSectionRef)} className="nav-item">Home</button>
                <button onClick={() => scrollToSection(aboutUsSectionRef)} className="nav-item">About Us</button>
                <button onClick={() => scrollToSection(reviewsSectionRef)} className="nav-item">Reviews</button>
                <button onClick={() => scrollToSection(contactUsRef)} className="nav-item">Contact Us</button>
                <div className="flex flex-col gap-3 pt-4 border-t border-gray-200 mt-4">
                  <Link to="/login">
                    <button className="btn btn-secondary w-full">Login</button>
                  </Link>
                  <Link to="/register">
                    <button className="btn btn-primary w-full">Sign Up</button>
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section ref={homeSectionRef} className="hero-section">
        <div className="container">
          <div className="hero-grid">
            {/* Hero Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="hero-content"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="hero-badge"
              >
                <TbSchool className="w-5 h-5 mr-2" />
                #1 Educational Platform in Tanzania
              </motion.div>

              <h1 className="hero-title">
                Fueling Bright Futures with{" "}
                <span className="text-gradient">
                  Education
                  <TbArrowBigRightLinesFilled className="inline w-8 h-8 ml-2" />
                </span>
              </h1>

              <p className="hero-subtitle">
                Discover limitless learning opportunities with our comprehensive
                online study platform. Study anywhere, anytime, and achieve your
                academic goals with confidence.
              </p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="cta-buttons"
              >
                <Link to="/register">
                  <button className="btn btn-primary">
                    Get Started Free
                  </button>
                </Link>
                <Link to="/login">
                  <button className="btn btn-secondary">
                    Sign In
                  </button>
                </Link>
              </motion.div>

              {/* Trust Indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="trust-indicators"
              >
                <div className="trust-indicator">
                  <TbUsers style={{color: '#007BFF'}} />
                  <span>15K+ Students</span>
                </div>
                <div className="trust-indicator">
                  <TbStar style={{color: '#f59e0b'}} />
                  <span>4.9/5 Rating</span>
                </div>
                <div className="trust-indicator">
                  <TbTrophy style={{color: '#007BFF'}} />
                  <span>Award Winning</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Hero Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="hero-image"
            >
              <div className="relative">
                <img
                  src={Image1}
                  alt="Students Learning"
                />

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="floating-element"
                  style={{top: '-1rem', left: '-1rem'}}
                >
                  <TbBook style={{color: '#007BFF'}} />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="floating-element"
                  style={{bottom: '-1rem', right: '-1rem'}}
                >
                  <TbTrophy style={{color: '#f59e0b'}} />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats-section">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="stats-grid"
          >
            {[
              { number: "15K+", text: "Active Students" },
              { number: "500+", text: "Expert Teachers" },
              { number: "1000+", text: "Video Lessons" },
              { number: "98%", text: "Success Rate" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="stat-item"
              >
                <div className="stat-number">{stat.number}</div>
                <p className="stat-text">{stat.text}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* About Section */}
      <section ref={aboutUsSectionRef} className="about-section">
        <div className="container">
          <div className="about-grid">
            <div className="about-content">
              <h2 className="about-title">Discover knowledge in limitless realms.</h2>
              <p className="about-text">
                Education serves as the cornerstone of personal and societal
                development. It is a dynamic process that empowers individuals with
                the knowledge, skills, and critical thinking abilities essential for
                success.
              </p>
              <Link to="/user/about-us" className="btn btn-primary">
                Learn More
              </Link>
            </div>
            <div className="about-image">
              <img src={Image1} alt="Students Learning" />
            </div>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section ref={reviewsSectionRef} className="reviews-section">
        <div className="reviews-container">
          <h2 className="reviews-title">
            Reviews from our students
          </h2>
          <div className="reviews-grid">
            {[
              {
                rating: 5,
                text: "BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.",
                user: { name: "Sarah Johnson" }
              },
              {
                rating: 5,
                text: "The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.",
                user: { name: "Michael Chen" }
              },
              {
                rating: 5,
                text: "Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!",
                user: { name: "Amina Hassan" }
              }
            ].map((review, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="review-card"
              >
                <div className="review-rating">
                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>
                    {'★'.repeat(review.rating)}
                  </div>
                </div>
                <div className="review-text">"{review.text}"</div>
                <div className="review-divider"></div>
                <div className="review-author">{review.user?.name}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section ref={contactUsRef} className="contact-section">
        <div className="contact-container">
          <h2 className="contact-title">Contact Us</h2>
          <p className="contact-subtitle">Get in touch with us for any questions or support</p>
          <form className="contact-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label className="form-label">Name</label>
              <input
                type="text"
                name="name"
                placeholder="Your Name"
                className="form-input"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label className="form-label">Email</label>
              <input
                type="email"
                name="email"
                placeholder="Your Email"
                className="form-input"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label className="form-label">Message</label>
              <textarea
                name="message"
                placeholder="Your Message"
                className="form-input form-textarea"
                value={formData.message}
                onChange={handleChange}
                required
              ></textarea>
            </div>
            <button
              type="submit"
              className="form-submit"
              disabled={loading}
            >
              {loading ? "Sending..." : "Send Message"}
            </button>
            {responseMessage && (
              <p className="response-message" style={{ marginTop: '1rem', textAlign: 'center', color: '#10b981' }}>
                {responseMessage}
              </p>
            )}
          </form>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <p className="footer-text">
            © 2024 BrainWave Educational Platform. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Home;
