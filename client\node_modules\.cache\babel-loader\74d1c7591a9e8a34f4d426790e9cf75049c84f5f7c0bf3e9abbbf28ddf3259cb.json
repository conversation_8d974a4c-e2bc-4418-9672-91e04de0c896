{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport Flag from \"../assets/tanzania-flag.png\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport { useTheme } from \"../contexts/ThemeContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      getUserData();\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: \"nav-modern bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-30 shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"BrainWave Educational Platform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-semibold text-gray-900\",\n                  children: user === null || user === void 0 ? void 0 : user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-600 font-medium\",\n                  children: user !== null && user !== void 0 && user.isAdmin ? \"Administrator\" : `Class ${user === null || user === void 0 ? void 0 : user.class} Student`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: Flag,\n                alt: \"Tanzania Flag\",\n                className: \"w-6 h-4 rounded border border-gray-200 shadow-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"NaPv4M1ve+2f5F0Lvw7be0jxD54=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useTheme];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "Flag", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "useTheme", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "getUserData", "response", "success", "data", "error", "token", "localStorage", "getItem", "includes", "activeRoute", "verifyPaymentStatus", "console", "log", "paymentStatus", "current", "clearInterval", "paymentRequired", "isAdmin", "setInterval", "getButtonClass", "title", "isDarkMode", "toggleTheme", "className", "header", "initial", "y", "opacity", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "class", "src", "alt", "div", "transition", "duration", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Flag from \"../assets/tanzania-flag.png\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport { useTheme } from \"../contexts/ThemeContext\";\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* No sidebar - users will use hub for navigation */}\r\n\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Header */}\r\n        <motion.header\r\n          initial={{ y: -20, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"nav-modern bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-30 shadow-sm\"\r\n        >\r\n          <div className=\"px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"flex items-center justify-between h-16\">\r\n              {/* Left Section - Page Title */}\r\n              <div className=\"flex items-center\">\r\n                <h1 className=\"text-xl font-bold text-gray-900\">\r\n                  BrainWave Educational Platform\r\n                </h1>\r\n              </div>\r\n\r\n              {/* Right Section - User Profile */}\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"text-right\">\r\n                  <div className=\"text-sm font-semibold text-gray-900\">{user?.name}</div>\r\n                  <div className=\"text-xs text-gray-600 font-medium\">\r\n                    {user?.isAdmin ? \"Administrator\" : `Class ${user?.class} Student`}\r\n                  </div>\r\n                </div>\r\n                <img src={Flag} alt=\"Tanzania Flag\" className=\"w-6 h-4 rounded border border-gray-200 shadow-sm\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content */}\r\n        <main className=\"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMyB,WAAW,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM;IAAEyB;EAAiB,CAAC,GAAGpB,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACK,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAGtB,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAM9B,MAAMwB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7B,WAAW,CAAC,CAAC;MACpC,IAAI6B,QAAQ,CAACC,OAAO,EAAE;QACpBJ,QAAQ,CAACvB,OAAO,CAAC0B,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM;QACLtC,OAAO,CAACuC,KAAK,CAACH,QAAQ,CAACpC,OAAO,CAAC;QAC/BkC,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdL,QAAQ,CAAC,QAAQ,CAAC;MAClBlC,OAAO,CAACuC,KAAK,CAACA,KAAK,CAACvC,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAMsC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTL,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLD,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINhC,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACiB,QAAQ,CAACC,WAAW,CAAC,EAAE;MACrEV,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEkB,WAAW,EAAEV,QAAQ,CAAC,CAAC;EAE7C,MAAMW,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMP,IAAI,GAAG,MAAMvB,kBAAkB,CAAC,CAAC;MACvC+B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAET,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,KAAK,IAAI,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,aAAa,MAAK,MAAM,EAAE;QACjD,IAAInB,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACjB,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACAW,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,QAAQ,CAACjB,eAAe,CAACsB,IAAI,CAAC,CAAC;QAC/B,IAAIV,WAAW,CAACqB,OAAO,EAAE;UACvBC,aAAa,CAACtB,WAAW,CAACqB,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdO,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAER,KAAK,CAAC;MACpDN,QAAQ,CAACjB,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/BW,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAEDzB,SAAS,CAAC,MAAM;IACd,IAAIsB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2B,eAAe,IAAI,EAAC3B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAIhB,yBAAyB,EAAE;QAC7Be,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CnB,WAAW,CAACqB,OAAO,GAAGI,WAAW,CAAC,MAAM;UACtCP,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCF,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTZ,QAAQ,CAAChB,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACc,yBAAyB,CAAC,CAAC;EAE/B7B,SAAS,CAAC,MAAM;IACd,IAAIsB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2B,eAAe,IAAI,EAAC3B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BF,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACrB,IAAI,EAAEoB,WAAW,CAAC,CAAC;EAGvB,MAAMU,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAI,CAAC/B,IAAI,CAAC2B,eAAe,IAAII,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAA1B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmB,aAAa,MAAK,MAAM,IAAIxB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2B,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAGD,MAAM;IAAEK,UAAU;IAAEC;EAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC;EAE9C,oBACEE,OAAA;IAAKsC,SAAS,EAAC,0CAA0C;IAAApC,QAAA,eAKvDF,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAApC,QAAA,gBAEhDF,OAAA,CAACf,MAAM,CAACsD,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BJ,SAAS,EAAC,8FAA8F;QAAApC,QAAA,eAExGF,OAAA;UAAKsC,SAAS,EAAC,sBAAsB;UAAApC,QAAA,eACnCF,OAAA;YAAKsC,SAAS,EAAC,wCAAwC;YAAApC,QAAA,gBAErDF,OAAA;cAAKsC,SAAS,EAAC,mBAAmB;cAAApC,QAAA,eAChCF,OAAA;gBAAIsC,SAAS,EAAC,iCAAiC;gBAAApC,QAAA,EAAC;cAEhD;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGN/C,OAAA;cAAKsC,SAAS,EAAC,6BAA6B;cAAApC,QAAA,gBAC1CF,OAAA;gBAAKsC,SAAS,EAAC,YAAY;gBAAApC,QAAA,gBACzBF,OAAA;kBAAKsC,SAAS,EAAC,qCAAqC;kBAAApC,QAAA,EAAEE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvE/C,OAAA;kBAAKsC,SAAS,EAAC,mCAAmC;kBAAApC,QAAA,EAC/CE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,OAAO,GAAG,eAAe,GAAI,SAAQ5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,KAAM;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/C,OAAA;gBAAKkD,GAAG,EAAEhE,IAAK;gBAACiE,GAAG,EAAC,eAAe;gBAACb,SAAS,EAAC;cAAkD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGhB/C,OAAA;QAAMsC,SAAS,EAAC,gEAAgE;QAAApC,QAAA,eAC9EF,OAAA,CAACf,MAAM,CAACmE,GAAG;UACTZ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BY,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CjB,SAAS,EAAC,QAAQ;UAAApC,QAAA,EAEjBA;QAAQ;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5C,EAAA,CA5JQF,cAAc;EAAA,QACJZ,WAAW,EAGCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EAiGQO,QAAQ;AAAA;AAAA0D,EAAA,GAxGrCvD,cAAc;AA8JvB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}