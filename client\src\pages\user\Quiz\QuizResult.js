import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { message } from 'antd';
import Confetti from 'react-confetti';
import useWindowSize from 'react-use/lib/useWindowSize';
import { getExamById } from '../../../apicalls/exams';
import { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import ContentRenderer from '../../../components/ContentRenderer';
import Pass from '../../../assets/pass.gif';
import Fail from '../../../assets/fail.gif';
import PassSound from '../../../assets/pass.mp3';
import FailSound from '../../../assets/fail.mp3';
import './responsive.css';

const QuizResult = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [explanations, setExplanations] = useState({});
  const [showReview, setShowReview] = useState(false);
  
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { width, height } = useWindowSize();
  
  const result = location.state?.result;

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());
        
        if (response.success) {
          setExamData(response.data);
          setQuestions(response.data?.questions || []);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  useEffect(() => {
    if (result) {
      // Play sound based on result
      new Audio(result.verdict === "Pass" ? PassSound : FailSound).play();
    }
  }, [result]);

  // Add quiz-fullscreen class for fullscreen experience
  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');

    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {
    try {
      dispatch(ShowLoading());
      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });
      dispatch(HideLoading());

      if (response.success) {
        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));
      } else {
        message.error(response.error || "Failed to fetch explanation.");
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  if (!result || !examData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (showReview) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Review Your Answers</h2>
              <p className="text-gray-600">Detailed breakdown of your quiz performance</p>
            </div>
          </div>

          {/* Questions Review */}
          <div className="space-y-6 mb-8">
            {questions.map((question, index) => {
              const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||
                                result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || "";
              const isCorrect = result.correctAnswers.some(q => q._id === question._id);

              return (
                <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200">
                  {/* Question Header */}
                  <div className={`px-6 py-4 border-b ${
                    isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">
                        Question {index + 1}
                      </h3>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        isCorrect
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      }`}>
                        {isCorrect ? '✓ Correct' : '✗ Incorrect'}
                      </span>
                    </div>
                  </div>

                  {/* Question Content */}
                  <div className="p-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-4">{question.name}</h4>

                    {(question.image || question.imageUrl) && (
                      <div className="mb-4">
                        <img
                          src={question.image || question.imageUrl}
                          alt="Question"
                          className="max-w-full h-auto rounded-lg border border-gray-200"
                          style={{ maxHeight: '300px' }}
                        />
                      </div>
                    )}

                    {/* Answer Comparison */}
                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      <div className={`p-4 rounded-lg border ${
                        isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                      }`}>
                        <h5 className="font-semibold text-gray-700 mb-2">Your Answer</h5>
                        <p className="text-gray-900">
                          {question.type === "mcq" || question.answerType === "Options"
                            ? question.options?.[userAnswer] || "Not answered"
                            : userAnswer || "Not answered"}
                        </p>
                      </div>

                      <div className="p-4 rounded-lg border border-green-200 bg-green-50">
                        <h5 className="font-semibold text-gray-700 mb-2">Correct Answer</h5>
                        <p className="text-gray-900">
                          {question.type === "mcq" || question.answerType === "Options"
                            ? question.options?.[question.correctOption || question.correctAnswer]
                            : (question.correctAnswer || question.correctOption)}
                        </p>
                      </div>
                    </div>

                    {/* Explanation Section */}
                    {!isCorrect && (
                      <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <button
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
                          onClick={() => fetchExplanation(
                            question.name,
                            question.type === "mcq" || question.answerType === "Options"
                              ? question.options?.[question.correctOption || question.correctAnswer]
                              : (question.correctAnswer || question.correctOption),
                            question.type === "mcq" || question.answerType === "Options"
                              ? question.options?.[userAnswer] || "Not answered"
                              : userAnswer || "Not answered",
                            question.image || question.imageUrl
                          )}
                        >
                          Get AI Explanation
                        </button>

                        {explanations[question.name] && (
                          <div className="mt-4 p-4 bg-white rounded-lg border border-blue-200">
                            <h6 className="font-semibold text-blue-800 mb-2">AI Explanation</h6>
                            <ContentRenderer text={explanations[question.name]} />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Back Button */}
          <div className="text-center">
            <button
              className="px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors duration-200 mr-4"
              onClick={() => setShowReview(false)}
            >
              Back to Results
            </button>
            <button
              className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
              onClick={() => navigate('/user/quiz')}
            >
              Back to Quizzes
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      {result.verdict === "Pass" && <Confetti width={width} height={height} />}

      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Header */}
          <div className={`px-6 py-12 text-center ${
            result.verdict === "Pass" ? "bg-green-50" : "bg-red-50"
          }`}>
            <img
              src={result.verdict === "Pass" ? Pass : Fail}
              alt={result.verdict}
              className="mx-auto mb-6"
              style={{ width: "120px", height: "120px" }}
            />
            <h1 className={`text-4xl font-bold mb-4 ${
              result.verdict === "Pass" ? "text-green-700" : "text-red-700"
            }`}>
              {result.verdict === "Pass" ? "Congratulations!" : "Better Luck Next Time!"}
            </h1>
            <p className="text-xl text-gray-600">
              {result.verdict === "Pass"
                ? "You've successfully passed the quiz!"
                : "Keep practicing and try again!"}
            </p>
          </div>

          {/* Statistics */}
          <div className="p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              <div className="text-center p-6 bg-green-50 rounded-xl">
                <div className="text-3xl font-bold text-green-600">
                  {result.correctAnswers?.length || 0}
                </div>
                <div className="text-sm text-green-700 font-medium">Correct</div>
              </div>

              <div className="text-center p-6 bg-red-50 rounded-xl">
                <div className="text-3xl font-bold text-red-600">
                  {result.wrongAnswers?.length || 0}
                </div>
                <div className="text-sm text-red-700 font-medium">Wrong</div>
              </div>

              <div className="text-center p-6 bg-blue-50 rounded-xl">
                <div className="text-3xl font-bold text-blue-600">
                  {questions.length}
                </div>
                <div className="text-sm text-blue-700 font-medium">Total</div>
              </div>

              <div className="text-center p-6 bg-purple-50 rounded-xl">
                <div className="text-3xl font-bold text-purple-600">
                  {examData.passingMarks}
                </div>
                <div className="text-sm text-purple-700 font-medium">Required</div>
              </div>
            </div>

            {/* Score Percentage */}
            <div className="mb-8">
              <div className="text-center mb-4">
                <span className="text-2xl font-bold text-gray-700">Your Score</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-6">
                <div
                  className={`h-full rounded-full transition-all duration-1000 ${
                    result.verdict === "Pass" ? "bg-green-600" : "bg-red-600"
                  }`}
                  style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}
                ></div>
              </div>
              <div className="text-center mt-3">
                <span className="text-3xl font-bold text-gray-700">
                  {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                className="px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200"
                onClick={() => setShowReview(true)}
              >
                Review Answers
              </button>

              <button
                className="px-8 py-4 bg-green-600 text-white rounded-xl font-semibold hover:bg-green-700 transition-colors duration-200"
                onClick={() => navigate(`/quiz/${id}/start`)}
              >
                Retake Quiz
              </button>

              <button
                className="px-8 py-4 bg-gray-600 text-white rounded-xl font-semibold hover:bg-gray-700 transition-colors duration-200"
                onClick={() => navigate('/user/quiz')}
              >
                Back to Quizzes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizResult;
