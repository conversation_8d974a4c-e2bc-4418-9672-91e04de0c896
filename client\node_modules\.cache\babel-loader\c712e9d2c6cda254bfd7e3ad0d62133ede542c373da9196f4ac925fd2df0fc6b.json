{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\StudyMaterial\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport { FaPlayCircle, FaBook, FaVideo, FaFileAlt, FaGraduationCap, FaDownload, FaEye, FaTimes, FaChevronDown, FaSearch, FaExpand, FaCompress } from \"react-icons/fa\";\nimport { TbVideo, TbFileText, TbBook as TbBookIcon, TbSchool, TbSearch, TbFilter, TbSortAscending, TbPlay, TbDownload, TbEye, TbCalendar, TbUser, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck, TbSubtitles, TbBooks, TbCertificate } from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction StudyMaterial() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary' ? primarySubjects : userLevelLower === 'secondary' ? secondarySubjects : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary' ? ['1', '2', '3', '4', '5', '6', '7'] : userLevelLower === 'secondary' ? ['Form-1', 'Form-2', 'Form-3', 'Form-4'] : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className) || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user === null || user === void 0 ? void 0 : user.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" : selectedClass.toString().replace(\"Form-\", \"\");\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject // This can be \"all\" or a specific subject\n      };\n\n      if (userLevel) {\n        data.level = userLevel;\n      }\n      const res = await getStudyMaterial(data);\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = tab => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n  const handleSubjectChange = subject => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n  const handleClassChange = className => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material => material.title.toLowerCase().includes(searchLower) || material.subject.toLowerCase().includes(searchLower) || material.year && material.year.toLowerCase().includes(searchLower));\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = documentUrl => {\n    fetch(documentUrl).then(response => response.blob()).then(blob => {\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = documentUrl.split(\"/\").pop();\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    }).catch(error => {\n      console.error(\"Error downloading the file:\", error);\n    });\n  };\n  const handleDocumentPreview = documentUrl => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedMaterials[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = language => {\n    setSelectedSubtitle(language);\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          credentials: 'include'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = material => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = event => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-r from-primary-600 to-blue-600 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-modern py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBooks, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-4xl font-bold mb-2\",\n                children: \"Study Materials\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-blue-100\",\n                children: [\"Access comprehensive learning resources for \", userLevel, \" education\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-blue-100 mb-1\",\n                children: \"Current Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-bold\",\n                children: userLevel === null || userLevel === void 0 ? void 0 : userLevel.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card p-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: [{\n              key: 'videos',\n              label: 'Videos',\n              icon: TbVideo,\n              color: 'text-red-600'\n            }, {\n              key: 'study-notes',\n              label: 'Notes',\n              icon: TbFileText,\n              color: 'text-blue-600'\n            }, {\n              key: 'past-papers',\n              label: 'Past Papers',\n              icon: TbCertificate,\n              color: 'text-purple-600'\n            }, {\n              key: 'books',\n              label: 'Books',\n              icon: TbBookIcon,\n              color: 'text-green-600'\n            }].map(tab => /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.key ? 'bg-primary-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n              onClick: () => handleTabChange(tab.key),\n              children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n                className: `w-5 h-5 ${activeTab === tab.key ? 'text-white' : tab.color}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tab.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), activeTab === tab.key && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeTab\",\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 21\n              }, this)]\n            }, tab.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row gap-6 items-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Search Materials\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                placeholder: `Search ${activeTab.replace('-', ' ')}...`,\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-64\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"Filter by Class\", userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-xs text-primary-600 font-medium\",\n                  children: [\"(Your class: \", userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: toggleClassSelector,\n                  className: \"w-full input-modern flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                      className: \"w-4 h-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: selectedClass === 'all' ? 'All Classes' : userLevelLower === 'primary' ? `Class ${selectedClass}` : `Form ${selectedClass}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 23\n                    }, this), selectedClass === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge-primary text-xs\",\n                      children: \"Current\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TbChevronDownIcon, {\n                    className: `w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                  children: showClassSelector && /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: -10\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -10\n                    },\n                    className: \"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'}`,\n                      onClick: () => handleClassChange('all'),\n                      children: \"All Classes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 25\n                    }, this), availableClasses.map((className, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'}`,\n                      onClick: () => handleClassChange(className),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 29\n                      }, this), className === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"badge-success text-xs\",\n                        children: \"Your Class\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 602,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-64\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Filter by Subject\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedSubject,\n                onChange: e => handleSubjectChange(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Subjects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 19\n                }, this), subjectsList.map((subject, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subject,\n                  children: subject\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-48\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Sort by\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: sortBy,\n                onChange: e => setSortBy(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"newest\",\n                  children: \"Newest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"oldest\",\n                  children: \"Oldest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"title\",\n                  children: \"By Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => {\n                setSearchTerm(\"\");\n                setSelectedClass(\"all\");\n                setSelectedSubject(\"all\");\n                setSortBy(\"newest\");\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this), (searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 pt-4 border-t border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Showing \", filteredAndSortedMaterials.length, \" of \", materials.length, \" \", activeTab.replace('-', ' ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"materials-section\",\n        children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading materials...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-state\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n            className: \"error-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Error Loading Materials\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"retry-btn\",\n            onClick: () => {\n              setError(null);\n              fetchMaterials();\n            },\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this) : filteredAndSortedMaterials.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"materials-grid\",\n          children: filteredAndSortedMaterials.map((material, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"material-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"material-type\",\n                children: [activeTab === 'videos' && /*#__PURE__*/_jsxDEV(FaVideo, {\n                  className: \"type-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 48\n                }, this), activeTab === 'study-notes' && /*#__PURE__*/_jsxDEV(FaFileAlt, {\n                  className: \"type-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 53\n                }, this), activeTab === 'past-papers' && /*#__PURE__*/_jsxDEV(FaFileAlt, {\n                  className: \"type-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 53\n                }, this), activeTab === 'books' && /*#__PURE__*/_jsxDEV(FaBook, {\n                  className: \"type-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 47\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-label\",\n                  children: activeTab === 'study-notes' ? 'Note' : activeTab === 'past-papers' ? 'Past Paper' : activeTab === 'videos' ? 'Video' : 'Book'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"header-right\",\n                children: [activeTab === 'videos' && material.coreClass && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"class-tags\",\n                  children: material.isCore ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"class-tag core-class\",\n                    children: [\"Core Class \", userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 27\n                  }, this) : material.sharedFromClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"class-tag shared-class\",\n                    children: [\"Shared from \", userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 23\n                }, this), material.year && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-year\",\n                  children: material.year\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this), activeTab === 'videos' && (material.videoUrl || material.videoID) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-thumbnail-container\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getThumbnailUrl(material),\n                alt: material.title,\n                className: \"video-thumbnail\",\n                onError: e => {\n                  // Fallback logic for failed thumbnails\n                  if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                    // For YouTube videos, try different quality thumbnails\n                    let videoId = material.videoID;\n                    if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                      videoId = match ? match[1] : videoId;\n                    }\n                    if (!e.target.src.includes('youtube.com')) {\n                      e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                    } else if (e.target.src.includes('maxresdefault')) {\n                      e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                    } else if (e.target.src.includes('mqdefault')) {\n                      e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                    } else {\n                      // Final fallback to default placeholder\n                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                    }\n                  } else {\n                    // For uploaded videos without thumbnails, use default placeholder\n                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"play-overlay\",\n                children: [/*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                  className: \"play-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"play-text\",\n                  children: \"Watch Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"material-title\",\n                children: material.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"material-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-subject\",\n                  children: material.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 21\n                }, this), material.className && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-class\",\n                  children: userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-actions\",\n              children: activeTab === 'videos' && (material.videoUrl || material.videoID) ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info-text\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-duration\",\n                  children: \"Click thumbnail to play\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 797,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 21\n              }, this) : material.documentUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn secondary\",\n                  onClick: () => handleDocumentPreview(material.documentUrl),\n                  children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 25\n                  }, this), \" View\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn primary\",\n                  onClick: () => handleDocumentDownload(material.documentUrl),\n                  children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 25\n                  }, this), \" Download\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"unavailable\",\n                children: \"Not available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n            className: \"empty-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Materials Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No study materials are available for your current selection.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"suggestion\",\n            children: \"Try selecting a different class or subject.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 7\n      }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n        onClick: e => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n          children: (() => {\n            const video = filteredAndSortedMaterials[currentVideoIndex];\n            if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Video not found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 34\n            }, this);\n            return /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: video.title || 'Untitled Video'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"video-subject\",\n                      children: video.subject || 'Unknown Subject'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"video-class\",\n                      children: [\"Class \", video.className || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-controls\",\n                  children: [!isVideoExpanded ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"control-btn expand-btn\",\n                    onClick: handleExpandVideo,\n                    title: \"Expand to fullscreen\",\n                    children: /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"control-btn collapse-btn\",\n                    onClick: handleCollapseVideo,\n                    title: \"Exit fullscreen\",\n                    children: /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 866,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"control-btn close-btn\",\n                    onClick: handleHideVideo,\n                    title: \"Close video\",\n                    children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-container\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '15px',\n                    background: '#000',\n                    borderRadius: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"400\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '400px',\n                      backgroundColor: '#000'\n                    },\n                    onError: e => {\n                      setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                    },\n                    onCanPlay: () => {\n                      setVideoError(null);\n                    },\n                    onLoadedMetadata: () => {\n                      // Auto-enable first subtitle if available and none selected\n                      if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                        const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                        handleSubtitleChange(defaultSubtitle.language);\n                      }\n                    },\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 31\n                    }, this)), /*#__PURE__*/_jsxDEV(\"p\", {\n                      style: {\n                        color: 'white',\n                        textAlign: 'center',\n                        padding: '20px'\n                      },\n                      children: [\"Your browser does not support the video tag.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 927,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                        href: video.signedVideoUrl || video.videoUrl,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        style: {\n                          color: '#4fc3f7'\n                        },\n                        children: \"Click here to open video in new tab\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 928,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 881,\n                    columnNumber: 27\n                  }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '12px 15px',\n                      background: 'rgba(0,0,0,0.8)',\n                      borderRadius: '0 0 8px 8px',\n                      borderTop: '1px solid #333'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '12px',\n                        flexWrap: 'wrap'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#fff',\n                          fontSize: '14px',\n                          fontWeight: '500',\n                          minWidth: 'fit-content'\n                        },\n                        children: \"\\uD83D\\uDCDD Choose Language:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 948,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          gap: '8px',\n                          flexWrap: 'wrap',\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleSubtitleChange('off'),\n                          style: {\n                            padding: '6px 12px',\n                            borderRadius: '20px',\n                            border: 'none',\n                            fontSize: '12px',\n                            fontWeight: '500',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease',\n                            backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                            color: '#fff'\n                          },\n                          children: \"OFF\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 964,\n                          columnNumber: 35\n                        }, this), video.subtitles.map(subtitle => /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleSubtitleChange(subtitle.language),\n                          style: {\n                            padding: '6px 12px',\n                            borderRadius: '20px',\n                            border: 'none',\n                            fontSize: '12px',\n                            fontWeight: '500',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease',\n                            backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                            color: '#fff',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '4px'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: subtitle.languageName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1001,\n                            columnNumber: 39\n                          }, this), subtitle.isAutoGenerated && /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              fontSize: '10px',\n                              opacity: 0.8,\n                              backgroundColor: 'rgba(255,255,255,0.2)',\n                              padding: '1px 4px',\n                              borderRadius: '8px'\n                            },\n                            children: \"AI\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1003,\n                            columnNumber: 41\n                          }, this)]\n                        }, subtitle.language, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 983,\n                          columnNumber: 37\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 957,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 942,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 23\n                }, this) : video.videoID ?\n                /*#__PURE__*/\n                // Fallback to YouTube embed if no videoUrl\n                _jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  className: \"video-iframe\",\n                  onLoad: () => console.log('✅ YouTube iframe loaded')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1034,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: video.signedVideoUrl || video.videoUrl,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"external-link-btn\",\n                      children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1036,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 19\n              }, this), !isVideoExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-footer\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-description\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Watch this educational video to learn more about \", video.subject, \".\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1051,\n                    columnNumber: 25\n                  }, this), video.subtitleGenerationStatus === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"subtitle-status\",\n                    style: {\n                      marginTop: '10px',\n                      fontSize: '0.9em',\n                      color: '#2196F3',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '16px',\n                        height: '16px',\n                        border: '2px solid #2196F3',\n                        borderTop: '2px solid transparent',\n                        borderRadius: '50%',\n                        animation: 'spin 1s linear infinite'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 29\n                    }, this), \"\\uD83E\\uDD16 Generating subtitles...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1050,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true);\n          })()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 833,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PDFModal, {\n        modalIsOpen: modalIsOpen,\n        closeModal: () => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        },\n        documentUrl: documentUrl\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1083,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 461,\n    columnNumber: 5\n  }, this);\n}\n_s(StudyMaterial, \"dZ3Gbn6nY2I1ns1Uyvd5JnsYVa0=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = StudyMaterial;\nexport default StudyMaterial;\nvar _c;\n$RefreshReg$(_c, \"StudyMaterial\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "PDFModal", "FaPlayCircle", "FaBook", "FaVideo", "FaFileAlt", "FaGraduationCap", "FaDownload", "FaEye", "FaTimes", "FaChevronDown", "FaSearch", "FaExpand", "FaCompress", "TbVideo", "TbFileText", "TbBook", "TbBookIcon", "TbSchool", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbPlay", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "TbSubtitles", "TbBooks", "TbCertificate", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StudyMaterial", "_s", "user", "state", "dispatch", "userLevel", "level", "userLevelLower", "toLowerCase", "subjectsList", "console", "log", "allPossibleClasses", "activeTab", "setActiveTab", "selectedClass", "setSelectedClass", "class", "className", "selectedSubject", "setSelectedSubject", "userCurrentClass", "materials", "setMaterials", "isLoading", "setIsLoading", "error", "setError", "showVideoIndices", "setShowVideoIndices", "modalIsOpen", "setModalIsOpen", "documentUrl", "setDocumentUrl", "availableClasses", "setAvailableClasses", "showClassSelector", "setShowClassSelector", "isVideoExpanded", "setIsVideoExpanded", "currentVideoIndex", "setCurrentVideoIndex", "videoError", "setVideoError", "selectedSubtitle", "setSelectedSubtitle", "videoRef", "setVideoRef", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "userClass", "length", "isValidSubject", "includes", "setAvailableClassesForLevel", "fetchMaterials", "normalizedClassName", "toString", "replace", "data", "content", "subject", "res", "status", "success", "handleTabChange", "tab", "handleSubjectChange", "handleClassChange", "toggleClassSelector", "filteredAndSortedMaterials", "filtered", "trim", "searchLower", "filter", "material", "title", "year", "sort", "a", "b", "parseInt", "localeCompare", "reverse", "handleDocumentDownload", "fetch", "then", "response", "blob", "url", "window", "URL", "createObjectURL", "document", "createElement", "href", "download", "split", "pop", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "catch", "handleDocumentPreview", "handleShowVideo", "index", "video", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "handleSubtitleChange", "language", "tracks", "textTracks", "i", "mode", "handleExpandVideo", "handleCollapseVideo", "encodeURIComponent", "method", "headers", "credentials", "ok", "Error", "json", "message", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "handleKeyPress", "event", "key", "addEventListener", "removeEventListener", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toUpperCase", "transition", "delay", "label", "icon", "color", "map", "button", "whileHover", "scale", "whileTap", "onClick", "layoutId", "placeholder", "value", "onChange", "e", "target", "exit", "coreClass", "isCore", "sharedFromClass", "src", "alt", "onError", "currentTarget", "style", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadedMetadata", "subtitles", "defaultSubtitle", "find", "sub", "isDefault", "crossOrigin", "type", "subtitle", "kind", "srcLang", "languageName", "default", "textAlign", "rel", "borderTop", "display", "alignItems", "gap", "flexWrap", "fontSize", "fontWeight", "min<PERSON><PERSON><PERSON>", "flex", "border", "cursor", "isAutoGenerated", "frameBorder", "allowFullScreen", "onLoad", "subtitleGenerationStatus", "marginTop", "animation", "closeModal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/StudyMaterial/index.js"], "sourcesContent": ["import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaPlayCircle,\n  FaBook,\n  FaVideo,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbFileText,\n  TbBook as TbBookIcon,\n  TbScho<PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON><PERSON><PERSON>,\n  Tb<PERSON>ortAscending,\n  Tb<PERSON><PERSON>,\n  TbD<PERSON>load,\n  Tb<PERSON><PERSON>,\n  TbCalendar,\n  Tb<PERSON>ser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  TbAlertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbSubtitles,\n  TbBooks,\n  TbCertificate\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'secondary'\n      ? secondarySubjects\n      : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    fetch(documentUrl)\n      .then((response) => response.blob())\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedMaterials[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = (language) => {\n    setSelectedSubtitle(language);\n\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n\n\n\n\n\n\n\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include'\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n\n\n\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = (material) => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = (event) => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Modern Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-primary-600 to-blue-600 text-white\"\n      >\n        <div className=\"container-modern py-12\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                <TbBooks className=\"w-8 h-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-4xl font-bold mb-2\">Study Materials</h1>\n                <p className=\"text-xl text-blue-100\">\n                  Access comprehensive learning resources for {userLevel} education\n                </p>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\">\n                <div className=\"text-sm text-blue-100 mb-1\">Current Level</div>\n                <div className=\"text-lg font-bold\">{userLevel?.toUpperCase()}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"container-modern py-8\">\n        {/* Modern Navigation Tabs */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-2\">\n            <div className=\"flex flex-wrap gap-2\">\n              {[\n                { key: 'videos', label: 'Videos', icon: TbVideo, color: 'text-red-600' },\n                { key: 'study-notes', label: 'Notes', icon: TbFileText, color: 'text-blue-600' },\n                { key: 'past-papers', label: 'Past Papers', icon: TbCertificate, color: 'text-purple-600' },\n                { key: 'books', label: 'Books', icon: TbBookIcon, color: 'text-green-600' }\n              ].map((tab) => (\n                <motion.button\n                  key={tab.key}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${\n                    activeTab === tab.key\n                      ? 'bg-primary-600 text-white shadow-md'\n                      : 'text-gray-600 hover:bg-gray-100'\n                  }`}\n                  onClick={() => handleTabChange(tab.key)}\n                >\n                  <tab.icon className={`w-5 h-5 ${activeTab === tab.key ? 'text-white' : tab.color}`} />\n                  <span>{tab.label}</span>\n                  {activeTab === tab.key && (\n                    <motion.div\n                      layoutId=\"activeTab\"\n                      className=\"w-2 h-2 bg-white rounded-full\"\n                    />\n                  )}\n                </motion.button>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Modern Filters Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-6\">\n            <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Search Materials\n                </label>\n                <input\n                  placeholder={`Search ${activeTab.replace('-', ' ')}...`}\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input\"\n                />\n              </div>\n\n              {/* Class Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Class\n                  {userCurrentClass && (\n                    <span className=\"ml-2 text-xs text-primary-600 font-medium\">\n                      (Your class: {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`})\n                    </span>\n                  )}\n                </label>\n                <div className=\"relative\">\n                  <button\n                    onClick={toggleClassSelector}\n                    className=\"w-full input-modern flex items-center justify-between\"\n                  >\n                    <span className=\"flex items-center space-x-2\">\n                      <TbSchool className=\"w-4 h-4 text-gray-400\" />\n                      <span>\n                        {selectedClass === 'all' ? 'All Classes' :\n                          userLevelLower === 'primary'\n                            ? `Class ${selectedClass}`\n                            : `Form ${selectedClass}`\n                        }\n                      </span>\n                      {selectedClass === userCurrentClass && (\n                        <span className=\"badge-primary text-xs\">Current</span>\n                      )}\n                    </span>\n                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  <AnimatePresence>\n                    {showClassSelector && (\n                      <motion.div\n                        initial={{ opacity: 0, y: -10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\"\n                      >\n                        <button\n                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${\n                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                          }`}\n                          onClick={() => handleClassChange('all')}\n                        >\n                          All Classes\n                        </button>\n                        {availableClasses.map((className, index) => (\n                          <button\n                            key={index}\n                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${\n                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                            }`}\n                            onClick={() => handleClassChange(className)}\n                          >\n                            <span>\n                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                            </span>\n                            {className === userCurrentClass && (\n                              <span className=\"badge-success text-xs\">Your Class</span>\n                            )}\n                          </button>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </div>\n\n              {/* Subject Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Subject\n                </label>\n                <select\n                  value={selectedSubject}\n                  onChange={(e) => handleSubjectChange(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"all\">All Subjects</option>\n                  {subjectsList.map((subject, index) => (\n                    <option key={index} value={subject}>\n                      {subject}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"w-full lg:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Sort by\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => {\n                  setSearchTerm(\"\");\n                  setSelectedClass(\"all\");\n                  setSelectedSubject(\"all\");\n                  setSortBy(\"newest\");\n                }}\n              >\n                Clear Filters\n              </button>\n            </div>\n\n            {/* Results Count */}\n            {(searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && (\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                <span className=\"text-sm text-gray-600\">\n                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}\n                </span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"materials-grid\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"material-card\">\n                <div className=\"card-header\">\n                  <div className=\"material-type\">\n                    {activeTab === 'videos' && <FaVideo className=\"type-icon\" />}\n                    {activeTab === 'study-notes' && <FaFileAlt className=\"type-icon\" />}\n                    {activeTab === 'past-papers' && <FaFileAlt className=\"type-icon\" />}\n                    {activeTab === 'books' && <FaBook className=\"type-icon\" />}\n                    <span className=\"type-label\">\n                      {activeTab === 'study-notes' ? 'Note' :\n                       activeTab === 'past-papers' ? 'Past Paper' :\n                       activeTab === 'videos' ? 'Video' : 'Book'}\n                    </span>\n                  </div>\n                  <div className=\"header-right\">\n                    {/* Class tags for videos */}\n                    {activeTab === 'videos' && material.coreClass && (\n                      <div className=\"class-tags\">\n                        {material.isCore ? (\n                          <span className=\"class-tag core-class\">\n                            Core Class {userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`}\n                          </span>\n                        ) : material.sharedFromClass && (\n                          <span className=\"class-tag shared-class\">\n                            Shared from {userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    )}\n                    {material.year && (\n                      <span className=\"material-year\">{material.year}</span>\n                    )}\n                  </div>\n                </div>\n\n                {/* Video Thumbnail for videos */}\n                {activeTab === 'videos' && (material.videoUrl || material.videoID) && (\n                  <div className=\"video-thumbnail-container\" onClick={() => handleShowVideo(index)}>\n                    <img\n                      src={getThumbnailUrl(material)}\n                      alt={material.title}\n                      className=\"video-thumbnail\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = material.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          if (!e.target.src.includes('youtube.com')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                          } else if (e.target.src.includes('maxresdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                          } else if (e.target.src.includes('mqdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                          } else {\n                            // Final fallback to default placeholder\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                          }\n                        } else {\n                          // For uploaded videos without thumbnails, use default placeholder\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                      <span className=\"play-text\">Watch Now</span>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {activeTab === 'videos' && (material.videoUrl || material.videoID) ? (\n                    <div className=\"video-info-text\">\n                      <span className=\"video-duration\">Click thumbnail to play</span>\n                    </div>\n                  ) : material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedMaterials[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <>\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3>{video.title || 'Untitled Video'}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject || 'Unknown Subject'}</span>\n                        <span className=\"video-class\">Class {video.className || 'N/A'}</span>\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      {!isVideoExpanded ? (\n                        <button\n                          className=\"control-btn expand-btn\"\n                          onClick={handleExpandVideo}\n                          title=\"Expand to fullscreen\"\n                        >\n                          <FaExpand />\n                        </button>\n                      ) : (\n                        <button\n                          className=\"control-btn collapse-btn\"\n                          onClick={handleCollapseVideo}\n                          title=\"Exit fullscreen\"\n                        >\n                          <FaCompress />\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadedMetadata={() => {\n                              // Auto-enable first subtitle if available and none selected\n                              if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                                const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                                handleSubtitleChange(defaultSubtitle.language);\n                              }\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            <p style={{color: 'white', textAlign: 'center', padding: '20px'}}>\n                              Your browser does not support the video tag.\n                              <br />\n                              <a href={video.signedVideoUrl || video.videoUrl} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#4fc3f7'}}>\n                                Click here to open video in new tab\n                              </a>\n                            </p>\n                          </video>\n\n                          {/* Custom Subtitle Controls */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div style={{\n                              padding: '12px 15px',\n                              background: 'rgba(0,0,0,0.8)',\n                              borderRadius: '0 0 8px 8px',\n                              borderTop: '1px solid #333'\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px',\n                                flexWrap: 'wrap'\n                              }}>\n                                <span style={{\n                                  color: '#fff',\n                                  fontSize: '14px',\n                                  fontWeight: '500',\n                                  minWidth: 'fit-content'\n                                }}>\n                                  📝 Choose Language:\n                                </span>\n\n                                <div style={{\n                                  display: 'flex',\n                                  gap: '8px',\n                                  flexWrap: 'wrap',\n                                  flex: 1\n                                }}>\n                                  {/* Off Button */}\n                                  <button\n                                    onClick={() => handleSubtitleChange('off')}\n                                    style={{\n                                      padding: '6px 12px',\n                                      borderRadius: '20px',\n                                      border: 'none',\n                                      fontSize: '12px',\n                                      fontWeight: '500',\n                                      cursor: 'pointer',\n                                      transition: 'all 0.2s ease',\n                                      backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                                      color: '#fff'\n                                    }}\n                                  >\n                                    OFF\n                                  </button>\n\n                                  {/* Language Buttons */}\n                                  {video.subtitles.map((subtitle) => (\n                                    <button\n                                      key={subtitle.language}\n                                      onClick={() => handleSubtitleChange(subtitle.language)}\n                                      style={{\n                                        padding: '6px 12px',\n                                        borderRadius: '20px',\n                                        border: 'none',\n                                        fontSize: '12px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s ease',\n                                        backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                                        color: '#fff',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '4px'\n                                      }}\n                                    >\n                                      <span>{subtitle.languageName}</span>\n                                      {subtitle.isAutoGenerated && (\n                                        <span style={{\n                                          fontSize: '10px',\n                                          opacity: 0.8,\n                                          backgroundColor: 'rgba(255,255,255,0.2)',\n                                          padding: '1px 4px',\n                                          borderRadius: '8px'\n                                        }}>\n                                          AI\n                                        </span>\n                                      )}\n                                    </button>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  {!isVideoExpanded && (\n                    <div className=\"video-footer\">\n                      <div className=\"video-description\">\n                        <p>Watch this educational video to learn more about {video.subject}.</p>\n                        {video.subtitleGenerationStatus === 'processing' && (\n                          <div className=\"subtitle-status\" style={{\n                            marginTop: '10px',\n                            fontSize: '0.9em',\n                            color: '#2196F3',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                          }}>\n                            <div style={{\n                              width: '16px',\n                              height: '16px',\n                              border: '2px solid #2196F3',\n                              borderTop: '2px solid transparent',\n                              borderRadius: '50%',\n                              animation: 'spin 1s linear infinite'\n                            }}></div>\n                            🤖 Generating subtitles...\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE;;AAEA,OAAOC,QAAQ,MAAM,YAAY;AACjC,SACEC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,UAAU,QACL,gBAAgB;AACvB,SACEC,OAAO,EACPC,UAAU,EACVC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,EACPC,WAAW,EACXC,OAAO,EACPC,aAAa,QACR,gBAAgB;AACvB,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjG,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGhD,WAAW,CAAEiD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGnD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoD,SAAS,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,KAAI,SAAS;EAC1C,MAAMC,cAAc,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC;EAC9C,MAAMC,YAAY,GAAGF,cAAc,KAAK,SAAS,GAC7Cd,eAAe,GACfc,cAAc,KAAK,WAAW,GAC5Bb,iBAAiB,GACjBC,eAAe;;EAErB;EACAhD,SAAS,CAAC,MAAM;IACd+D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEN,SAAS,CAAC;IAC1DK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,cAAc,CAAC;IAC3EG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEF,YAAY,CAAC;IAChEC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAET,IAAI,CAAC;EACtD,CAAC,EAAE,CAACG,SAAS,EAAEE,cAAc,EAAEE,YAAY,EAAEP,IAAI,CAAC,CAAC;;EAEnD;EACA,MAAMU,kBAAkB,GAAGL,cAAc,KAAK,SAAS,GACnD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GACnCA,cAAc,KAAK,WAAW,GAC5B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,GACxC,CAAC,QAAQ,EAAE,QAAQ,CAAC;;EAE1B;EACA,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,CAAAwD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,KAAI,KAAK,CAAC;EAC3F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM2E,gBAAgB,GAAG,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;EACvD,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8E,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4F,eAAe,EAAEC,kBAAkB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoG,QAAQ,EAAEC,WAAW,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;;EAG9C;EACA,MAAM,CAACsG,UAAU,EAAEC,aAAa,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwG,MAAM,EAAEC,SAAS,CAAC,GAAGzG,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyG,SAAS,GAAG,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;IAChD,IAAIkC,SAAS,IAAIrC,aAAa,KAAK,KAAK,IAAI,CAACmB,gBAAgB,CAACmB,MAAM,EAAE;MACpErC,gBAAgB,CAACoC,SAAS,CAAC;IAC7B;EACF,CAAC,EAAE,CAAClD,IAAI,EAAEa,aAAa,EAAEmB,gBAAgB,CAACmB,MAAM,CAAC,CAAC;;EAElD;EACA1G,SAAS,CAAC,MAAM;IACd,IAAIuD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,KAAK,EAAE;MACf;MACA,MAAMgD,cAAc,GAAG7C,YAAY,CAAC8C,QAAQ,CAACpC,eAAe,CAAC;MAC7D,IAAI,CAACmC,cAAc,IAAInC,eAAe,KAAK,KAAK,EAAE;QAChDT,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjES,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAAClB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,EAAEG,YAAY,EAAEU,eAAe,CAAC,CAAC;;EAEhD;EACA,MAAMqC,2BAA2B,GAAG5G,WAAW,CAAC,MAAM;IACpDuF,mBAAmB,CAACvB,kBAAkB,CAAC;EACzC,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAM6C,cAAc,GAAG7G,WAAW,CAAC,YAAY;IAC7C,IAAI,CAACiE,SAAS,IAAIE,aAAa,KAAK,SAAS,EAAE;MAC7C;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACdvB,QAAQ,CAAChD,WAAW,CAAC,CAAC,CAAC;IAEvB,IAAI;MACF;MACA,MAAMsG,mBAAmB,GAAG3C,aAAa,KAAK,KAAK,GAAG,KAAK,GACzDA,aAAa,CAAC4C,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAE/C,MAAMC,IAAI,GAAG;QACXC,OAAO,EAAEjD,SAAS;QAClBK,SAAS,EAAEwC,mBAAmB;QAC9BK,OAAO,EAAE5C,eAAe,CAAE;MAC5B,CAAC;;MACD,IAAId,SAAS,EAAE;QACbwD,IAAI,CAACvD,KAAK,GAAGD,SAAS;MACxB;MAEA,MAAM2D,GAAG,GAAG,MAAMhH,gBAAgB,CAAC6G,IAAI,CAAC;MAExC,IAAIG,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACH,IAAI,CAACK,OAAO,EAAE;QAC1C,MAAM5C,SAAS,GAAG0C,GAAG,CAACH,IAAI,CAACA,IAAI,KAAK,OAAO,GAAG,EAAE,GAAGG,GAAG,CAACH,IAAI,CAACA,IAAI;QAChEtC,YAAY,CAACD,SAAS,CAAC;MACzB,CAAC,MAAM;QACLC,YAAY,CAAC,EAAE,CAAC;QAChBI,QAAQ,CAAE,mBAAkBd,SAAU,qBAAoB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDH,YAAY,CAAC,EAAE,CAAC;MAChBI,QAAQ,CAAE,kBAAiBd,SAAU,+CAA8C,CAAC;IACtF,CAAC,SAAS;MACRY,YAAY,CAAC,KAAK,CAAC;MACnBrB,QAAQ,CAACjD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC0D,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAEd,SAAS,EAAED,QAAQ,CAAC,CAAC;;EAEpE;EACAzD,SAAS,CAAC,MAAM;IACd,IAAIuD,IAAI,IAAIG,SAAS,EAAE;MACrBmD,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACtD,IAAI,EAAEG,SAAS,EAAEmD,2BAA2B,CAAC,CAAC;;EAElD;EACA7G,SAAS,CAAC,MAAM;IACd;IACA,IAAIuD,IAAI,IAAIG,SAAS,IAAIQ,SAAS,IAAIE,aAAa,IAAIA,aAAa,KAAK,SAAS,EAAE;MAClF0C,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACvD,IAAI,EAAEG,SAAS,EAAEQ,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAEsC,cAAc,CAAC,CAAC;;EAEhF;EACA,MAAMU,eAAe,GAAIC,GAAG,IAAK;IAC/B7C,YAAY,CAAC,EAAE,CAAC;IAChBT,YAAY,CAACsD,GAAG,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBE,SAAS,CAAC,QAAQ,CAAC;EACrB,CAAC;EAED,MAAMkB,mBAAmB,GAAIN,OAAO,IAAK;IACvCxC,YAAY,CAAC,EAAE,CAAC;IAChBH,kBAAkB,CAAC2C,OAAO,CAAC;IAC3Bd,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMqB,iBAAiB,GAAIpD,SAAS,IAAK;IACvCK,YAAY,CAAC,EAAE,CAAC;IAChBP,gBAAgB,CAACE,SAAS,CAAC;IAC3BmB,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMkC,mBAAmB,GAAGA,CAAA,KAAM;IAChClC,oBAAoB,CAAC,CAACD,iBAAiB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMoC,0BAA0B,GAAG3H,OAAO,CAAC,MAAM;IAC/C,IAAI,CAACyE,SAAS,IAAIA,SAAS,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,EAAE;IACX;IAEA,IAAIoB,QAAQ,GAAGnD,SAAS;;IAExB;IACA,IAAI0B,UAAU,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAG3B,UAAU,CAACxC,WAAW,CAAC,CAAC;MAC5CiE,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IACjCA,QAAQ,CAACC,KAAK,CAACtE,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACoB,WAAW,CAAC,IAClDE,QAAQ,CAACd,OAAO,CAACvD,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACoB,WAAW,CAAC,IACnDE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACvE,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACoB,WAAW,CACpE,CAAC;IACH;;IAEA;IACAF,QAAQ,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIhC,MAAM,KAAK,QAAQ,EAAE;QACvB;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC,GAAGI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC;QAC5C;QACA;QAAA,KACK,IAAIlE,SAAS,KAAK,QAAQ,EAAE;UAC/B;UACA,OAAO,CAAC,CAAC,CAAC;QACZ;QACA;QAAA,KACK,IAAIoE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM,IAAI7B,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC,GAAGI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC;QAC5C;QACA;QAAA,KACK,IAAIlE,SAAS,KAAK,QAAQ,EAAE;UAC/B,OAAO,CAAC,CAAC,CAAC;QACZ;QACA;QAAA,KACK,IAAIoE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM;QACL;QACA,OAAOE,CAAC,CAACH,KAAK,CAACM,aAAa,CAACF,CAAC,CAACJ,KAAK,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIjE,SAAS,KAAK,QAAQ,IAAIqC,MAAM,KAAK,QAAQ,EAAE;MACjDuB,QAAQ,CAACY,OAAO,CAAC,CAAC;IACpB;IAEA,OAAOZ,QAAQ;EACjB,CAAC,EAAE,CAACnD,SAAS,EAAE0B,UAAU,EAAEE,MAAM,EAAErC,SAAS,CAAC,CAAC;;EAE9C;EACA,MAAMyE,sBAAsB,GAAItD,WAAW,IAAK;IAC9CuD,KAAK,CAACvD,WAAW,CAAC,CACfwD,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEE,IAAI,IAAK;MACd,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMT,CAAC,GAAGc,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCf,CAAC,CAACgB,IAAI,GAAGN,GAAG;MACZV,CAAC,CAACiB,QAAQ,GAAGlE,WAAW,CAACmE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACzCL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACrB,CAAC,CAAC;MAC5BA,CAAC,CAACsB,KAAK,CAAC,CAAC;MACTR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACvB,CAAC,CAAC;MAC5BW,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC;IACjC,CAAC,CAAC,CACDe,KAAK,CAAEhF,KAAK,IAAK;MAChBhB,OAAO,CAACgB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CAAC;EACN,CAAC;EAED,MAAMiF,qBAAqB,GAAI3E,WAAW,IAAK;IAC7CC,cAAc,CAACD,WAAW,CAAC;IAC3BD,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAM6E,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMC,KAAK,GAAGtC,0BAA0B,CAACqC,KAAK,CAAC;IAE/CpE,oBAAoB,CAACoE,KAAK,CAAC;IAC3BhF,mBAAmB,CAAC,CAACgF,KAAK,CAAC,CAAC;IAC5BtE,kBAAkB,CAAC,KAAK,CAAC;IACzBI,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAImE,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,QAAQ,KAAKD,KAAK,CAACC,QAAQ,CAACxD,QAAQ,CAAC,eAAe,CAAC,IAAIuD,KAAK,CAACC,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMyD,SAAS,GAAG,MAAMC,iBAAiB,CAACH,KAAK,CAACC,QAAQ,CAAC;QACzDD,KAAK,CAACI,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAOtF,KAAK,EAAE;QACdhB,OAAO,CAACyG,IAAI,CAAC,8CAA8C,CAAC;QAC5DL,KAAK,CAACI,cAAc,GAAGJ,KAAK,CAACC,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BvF,mBAAmB,CAAC,EAAE,CAAC;IACvBY,oBAAoB,CAAC,IAAI,CAAC;IAC1BF,kBAAkB,CAAC,KAAK,CAAC;IACzBI,aAAa,CAAC,IAAI,CAAC;IACnBE,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMsE,oBAAoB,GAAIC,QAAQ,IAAK;IACzCzE,mBAAmB,CAACyE,QAAQ,CAAC;IAE7B,IAAIxE,QAAQ,EAAE;MACZ,MAAMyE,MAAM,GAAGzE,QAAQ,CAAC0E,UAAU;;MAElC;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAClE,MAAM,EAAEoE,CAAC,EAAE,EAAE;QACtCF,MAAM,CAACE,CAAC,CAAC,CAACC,IAAI,GAAG,UAAU;MAC7B;;MAEA;MACA,IAAIJ,QAAQ,KAAK,KAAK,EAAE;QACtB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAClE,MAAM,EAAEoE,CAAC,EAAE,EAAE;UACtC,IAAIF,MAAM,CAACE,CAAC,CAAC,CAACH,QAAQ,KAAKA,QAAQ,EAAE;YACnCC,MAAM,CAACE,CAAC,CAAC,CAACC,IAAI,GAAG,SAAS;YAC1B;UACF;QACF;MACF;IACF;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMqF,mBAAmB,GAAGA,CAAA,KAAM;IAChCrF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAUD;;EAEA;EACA,MAAM0E,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACxD,QAAQ,CAAC,eAAe,CAAC,IAAIwD,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMkC,QAAQ,GAAG,MAAMF,KAAK,CAAE,6DAA4DsC,kBAAkB,CAACd,QAAQ,CAAE,EAAC,EAAE;UACxHe,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,WAAW,EAAE;QACf,CAAC,CAAC;QAEF,IAAI,CAACvC,QAAQ,CAACwC,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBzC,QAAQ,CAACxB,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAMJ,IAAI,GAAG,MAAM4B,QAAQ,CAAC0C,IAAI,CAAC,CAAC;QAElC,IAAItE,IAAI,CAACK,OAAO,IAAIL,IAAI,CAACmD,SAAS,EAAE;UAClCtG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAOkD,IAAI,CAACmD,SAAS;QACvB,CAAC,MAAM;UACLtG,OAAO,CAACyG,IAAI,CAAC,+CAA+C,EAAEtD,IAAI,CAAC;UACnE,OAAOkD,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;QACdhB,OAAO,CAACyG,IAAI,CAAC,8CAA8C,EAAEzF,KAAK,CAAC0G,OAAO,CAAC;QAC3E,OAAOrB,QAAQ;MACjB;IACF;;IAEA;IACA,OAAOA,QAAQ;EACjB,CAAC;;EAMD;EACA,MAAMsB,eAAe,GAAIxD,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,CAACyD,SAAS,IAAIzD,QAAQ,CAACyD,SAAS,KAAK,EAAE,IAAIzD,QAAQ,CAACyD,SAAS,KAAK,YAAY,EAAE;MAC1F,OAAOzD,QAAQ,CAACyD,SAAS;IAC3B;;IAEA;IACA,IAAIzD,QAAQ,CAAC0D,OAAO,IAAI,CAAC1D,QAAQ,CAAC0D,OAAO,CAAChF,QAAQ,CAAC,eAAe,CAAC,EAAE;MACnE;MACA,IAAIiF,OAAO,GAAG3D,QAAQ,CAAC0D,OAAO;MAC9B,IAAIC,OAAO,CAACjF,QAAQ,CAAC,aAAa,CAAC,IAAIiF,OAAO,CAACjF,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAMkF,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;;IAEA;IACA,OAAO,4cAA4c;EACrd,CAAC;;EAED;EACA7L,SAAS,CAAC,MAAM;IACd,MAAM+L,cAAc,GAAIC,KAAK,IAAK;MAChC,IAAI/G,gBAAgB,CAACyB,MAAM,GAAG,CAAC,EAAE;QAC/B,QAAQsF,KAAK,CAACC,GAAG;UACf,KAAK,QAAQ;YACXxB,eAAe,CAAC,CAAC;YACjB;UACF,KAAK,GAAG;UACR,KAAK,GAAG;YACN,IAAI,CAAC9E,eAAe,EAAE;cACpBqF,iBAAiB,CAAC,CAAC;YACrB,CAAC,MAAM;cACLC,mBAAmB,CAAC,CAAC;YACvB;YACA;UACF;YACE;QACJ;MACF;IACF,CAAC;IAED7B,QAAQ,CAAC8C,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;IACpD,OAAO,MAAM;MACX3C,QAAQ,CAAC+C,mBAAmB,CAAC,SAAS,EAAEJ,cAAc,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAAC9G,gBAAgB,EAAEU,eAAe,CAAC,CAAC;EAEvC,oBACEzC,OAAA;IAAKqB,SAAS,EAAC,wDAAwD;IAAA6H,QAAA,gBAErElJ,OAAA,CAAC/C,MAAM,CAACkM,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BjI,SAAS,EAAC,0DAA0D;MAAA6H,QAAA,eAEpElJ,OAAA;QAAKqB,SAAS,EAAC,wBAAwB;QAAA6H,QAAA,eACrClJ,OAAA;UAAKqB,SAAS,EAAC,mCAAmC;UAAA6H,QAAA,gBAChDlJ,OAAA;YAAKqB,SAAS,EAAC,6BAA6B;YAAA6H,QAAA,gBAC1ClJ,OAAA;cAAKqB,SAAS,EAAC,qEAAqE;cAAA6H,QAAA,eAClFlJ,OAAA,CAACN,OAAO;gBAAC2B,SAAS,EAAC;cAAoB;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN3J,OAAA;cAAAkJ,QAAA,gBACElJ,OAAA;gBAAIqB,SAAS,EAAC,yBAAyB;gBAAA6H,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D3J,OAAA;gBAAGqB,SAAS,EAAC,uBAAuB;gBAAA6H,QAAA,GAAC,8CACS,EAAC1I,SAAS,EAAC,YACzD;cAAA;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3J,OAAA;YAAKqB,SAAS,EAAC,iBAAiB;YAAA6H,QAAA,eAC9BlJ,OAAA;cAAKqB,SAAS,EAAC,mDAAmD;cAAA6H,QAAA,gBAChElJ,OAAA;gBAAKqB,SAAS,EAAC,4BAA4B;gBAAA6H,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/D3J,OAAA;gBAAKqB,SAAS,EAAC,mBAAmB;gBAAA6H,QAAA,EAAE1I,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEoJ,WAAW,CAAC;cAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEb3J,OAAA;MAAKqB,SAAS,EAAC,uBAAuB;MAAA6H,QAAA,gBAEpClJ,OAAA,CAAC/C,MAAM,CAACkM,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BzI,SAAS,EAAC,MAAM;QAAA6H,QAAA,eAEhBlJ,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAA6H,QAAA,eACvBlJ,OAAA;YAAKqB,SAAS,EAAC,sBAAsB;YAAA6H,QAAA,EAClC,CACC;cAAEH,GAAG,EAAE,QAAQ;cAAEgB,KAAK,EAAE,QAAQ;cAAEC,IAAI,EAAE3L,OAAO;cAAE4L,KAAK,EAAE;YAAe,CAAC,EACxE;cAAElB,GAAG,EAAE,aAAa;cAAEgB,KAAK,EAAE,OAAO;cAAEC,IAAI,EAAE1L,UAAU;cAAE2L,KAAK,EAAE;YAAgB,CAAC,EAChF;cAAElB,GAAG,EAAE,aAAa;cAAEgB,KAAK,EAAE,aAAa;cAAEC,IAAI,EAAErK,aAAa;cAAEsK,KAAK,EAAE;YAAkB,CAAC,EAC3F;cAAElB,GAAG,EAAE,OAAO;cAAEgB,KAAK,EAAE,OAAO;cAAEC,IAAI,EAAExL,UAAU;cAAEyL,KAAK,EAAE;YAAiB,CAAC,CAC5E,CAACC,GAAG,CAAE3F,GAAG,iBACRvE,OAAA,CAAC/C,MAAM,CAACkN,MAAM;cAEZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BhJ,SAAS,EAAG,4FACVL,SAAS,KAAKuD,GAAG,CAACwE,GAAG,GACjB,qCAAqC,GACrC,iCACL,EAAE;cACHwB,OAAO,EAAEA,CAAA,KAAMjG,eAAe,CAACC,GAAG,CAACwE,GAAG,CAAE;cAAAG,QAAA,gBAExClJ,OAAA,CAACuE,GAAG,CAACyF,IAAI;gBAAC3I,SAAS,EAAG,WAAUL,SAAS,KAAKuD,GAAG,CAACwE,GAAG,GAAG,YAAY,GAAGxE,GAAG,CAAC0F,KAAM;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtF3J,OAAA;gBAAAkJ,QAAA,EAAO3E,GAAG,CAACwF;cAAK;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACvB3I,SAAS,KAAKuD,GAAG,CAACwE,GAAG,iBACpB/I,OAAA,CAAC/C,MAAM,CAACkM,GAAG;gBACTqB,QAAQ,EAAC,WAAW;gBACpBnJ,SAAS,EAAC;cAA+B;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACF;YAAA,GAjBIpF,GAAG,CAACwE,GAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBC,CAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb3J,OAAA,CAAC/C,MAAM,CAACkM,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BzI,SAAS,EAAC,MAAM;QAAA6H,QAAA,eAEhBlJ,OAAA;UAAKqB,SAAS,EAAC,UAAU;UAAA6H,QAAA,gBACvBlJ,OAAA;YAAKqB,SAAS,EAAC,2CAA2C;YAAA6H,QAAA,gBAExDlJ,OAAA;cAAKqB,SAAS,EAAC,QAAQ;cAAA6H,QAAA,gBACrBlJ,OAAA;gBAAOqB,SAAS,EAAC,8CAA8C;gBAAA6H,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3J,OAAA;gBACEyK,WAAW,EAAG,UAASzJ,SAAS,CAAC+C,OAAO,CAAC,GAAG,EAAE,GAAG,CAAE,KAAK;gBACxD2G,KAAK,EAAEvH,UAAW;gBAClBwH,QAAQ,EAAGC,CAAC,IAAKxH,aAAa,CAACwH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CrJ,SAAS,EAAC;cAAY;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN3J,OAAA;cAAKqB,SAAS,EAAC,gBAAgB;cAAA6H,QAAA,gBAC7BlJ,OAAA;gBAAOqB,SAAS,EAAC,8CAA8C;gBAAA6H,QAAA,GAAC,iBAE9D,EAAC1H,gBAAgB,iBACfxB,OAAA;kBAAMqB,SAAS,EAAC,2CAA2C;kBAAA6H,QAAA,GAAC,eAC7C,EAACxI,cAAc,KAAK,SAAS,GAAI,SAAQc,gBAAiB,EAAC,GAAI,QAAOA,gBAAiB,EAAC,EAAC,GACxG;gBAAA;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACR3J,OAAA;gBAAKqB,SAAS,EAAC,UAAU;gBAAA6H,QAAA,gBACvBlJ,OAAA;kBACEuK,OAAO,EAAE7F,mBAAoB;kBAC7BrD,SAAS,EAAC,uDAAuD;kBAAA6H,QAAA,gBAEjElJ,OAAA;oBAAMqB,SAAS,EAAC,6BAA6B;oBAAA6H,QAAA,gBAC3ClJ,OAAA,CAACvB,QAAQ;sBAAC4C,SAAS,EAAC;oBAAuB;sBAAAmI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9C3J,OAAA;sBAAAkJ,QAAA,EACGhI,aAAa,KAAK,KAAK,GAAG,aAAa,GACtCR,cAAc,KAAK,SAAS,GACvB,SAAQQ,aAAc,EAAC,GACvB,QAAOA,aAAc;oBAAC;sBAAAsI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEzB,CAAC,EACNzI,aAAa,KAAKM,gBAAgB,iBACjCxB,OAAA;sBAAMqB,SAAS,EAAC,uBAAuB;sBAAA6H,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACtD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACP3J,OAAA,CAACb,iBAAiB;oBAACkC,SAAS,EAAG,8CAA6CkB,iBAAiB,GAAG,YAAY,GAAG,EAAG;kBAAE;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC,eAET3J,OAAA,CAAC9C,eAAe;kBAAAgM,QAAA,EACb3G,iBAAiB,iBAChBvC,OAAA,CAAC/C,MAAM,CAACkM,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BwB,IAAI,EAAE;sBAAEzB,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BjI,SAAS,EAAC,0HAA0H;oBAAA6H,QAAA,gBAEpIlJ,OAAA;sBACEqB,SAAS,EAAG,iEACVH,aAAa,KAAK,KAAK,GAAG,4CAA4C,GAAG,eAC1E,EAAE;sBACHqJ,OAAO,EAAEA,CAAA,KAAM9F,iBAAiB,CAAC,KAAK,CAAE;sBAAAyE,QAAA,EACzC;oBAED;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRtH,gBAAgB,CAAC6H,GAAG,CAAC,CAAC7I,SAAS,EAAE2F,KAAK,kBACrChH,OAAA;sBAEEqB,SAAS,EAAG,mGACVH,aAAa,KAAKG,SAAS,GAAG,4CAA4C,GAAG,eAC9E,EAAE;sBACHkJ,OAAO,EAAEA,CAAA,KAAM9F,iBAAiB,CAACpD,SAAS,CAAE;sBAAA6H,QAAA,gBAE5ClJ,OAAA;wBAAAkJ,QAAA,EACGxI,cAAc,KAAK,SAAS,GAAI,SAAQW,SAAU,EAAC,GAAI,QAAOA,SAAU;sBAAC;wBAAAmI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CAAC,EACNtI,SAAS,KAAKG,gBAAgB,iBAC7BxB,OAAA;wBAAMqB,SAAS,EAAC,uBAAuB;wBAAA6H,QAAA,EAAC;sBAAU;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACzD;oBAAA,GAXI3C,KAAK;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAYJ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACc,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3J,OAAA;cAAKqB,SAAS,EAAC,gBAAgB;cAAA6H,QAAA,gBAC7BlJ,OAAA;gBAAOqB,SAAS,EAAC,8CAA8C;gBAAA6H,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3J,OAAA;gBACE0K,KAAK,EAAEpJ,eAAgB;gBACvBqJ,QAAQ,EAAGC,CAAC,IAAKpG,mBAAmB,CAACoG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACrDrJ,SAAS,EAAC,cAAc;gBAAA6H,QAAA,gBAExBlJ,OAAA;kBAAQ0K,KAAK,EAAC,KAAK;kBAAAxB,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxC/I,YAAY,CAACsJ,GAAG,CAAC,CAAChG,OAAO,EAAE8C,KAAK,kBAC/BhH,OAAA;kBAAoB0K,KAAK,EAAExG,OAAQ;kBAAAgF,QAAA,EAChChF;gBAAO,GADG8C,KAAK;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN3J,OAAA;cAAKqB,SAAS,EAAC,gBAAgB;cAAA6H,QAAA,gBAC7BlJ,OAAA;gBAAOqB,SAAS,EAAC,8CAA8C;gBAAA6H,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3J,OAAA;gBACE0K,KAAK,EAAErH,MAAO;gBACdsH,QAAQ,EAAGC,CAAC,IAAKtH,SAAS,CAACsH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC3CrJ,SAAS,EAAC,cAAc;gBAAA6H,QAAA,gBAExBlJ,OAAA;kBAAQ0K,KAAK,EAAC,QAAQ;kBAAAxB,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C3J,OAAA;kBAAQ0K,KAAK,EAAC,QAAQ;kBAAAxB,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C3J,OAAA;kBAAQ0K,KAAK,EAAC,OAAO;kBAAAxB,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN3J,OAAA;cACEqB,SAAS,EAAC,mBAAmB;cAC7BkJ,OAAO,EAAEA,CAAA,KAAM;gBACbnH,aAAa,CAAC,EAAE,CAAC;gBACjBjC,gBAAgB,CAAC,KAAK,CAAC;gBACvBI,kBAAkB,CAAC,KAAK,CAAC;gBACzB+B,SAAS,CAAC,QAAQ,CAAC;cACrB,CAAE;cAAA4F,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGL,CAACxG,UAAU,IAAIjC,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,kBAClEtB,OAAA;YAAKqB,SAAS,EAAC,oCAAoC;YAAA6H,QAAA,eACjDlJ,OAAA;cAAMqB,SAAS,EAAC,uBAAuB;cAAA6H,QAAA,GAAC,UAC9B,EAACvE,0BAA0B,CAACnB,MAAM,EAAC,MAAI,EAAC/B,SAAS,CAAC+B,MAAM,EAAC,GAAC,EAACxC,SAAS,CAAC+C,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;YAAA;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGf3J,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAAA6H,QAAA,EAC/BvH,SAAS,gBACR3B,OAAA;UAAKqB,SAAS,EAAC,eAAe;UAAA6H,QAAA,gBAC5BlJ,OAAA;YAAKqB,SAAS,EAAC;UAAiB;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC3J,OAAA;YAAAkJ,QAAA,EAAG;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,GACJ9H,KAAK,gBACP7B,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAA6H,QAAA,gBAC1BlJ,OAAA,CAAChC,OAAO;YAACqD,SAAS,EAAC;UAAY;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClC3J,OAAA;YAAAkJ,QAAA,EAAI;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC3J,OAAA;YAAAkJ,QAAA,EAAIrH;UAAK;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd3J,OAAA;YACEqB,SAAS,EAAC,WAAW;YACrBkJ,OAAO,EAAEA,CAAA,KAAM;cACbzI,QAAQ,CAAC,IAAI,CAAC;cACd8B,cAAc,CAAC,CAAC;YAClB,CAAE;YAAAsF,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,GACJhF,0BAA0B,CAACnB,MAAM,GAAG,CAAC,gBACvCxD,OAAA;UAAKqB,SAAS,EAAC,gBAAgB;UAAA6H,QAAA,EAC5BvE,0BAA0B,CAACuF,GAAG,CAAC,CAAClF,QAAQ,EAAEgC,KAAK,kBAC9ChH,OAAA;YAAiBqB,SAAS,EAAC,eAAe;YAAA6H,QAAA,gBACxClJ,OAAA;cAAKqB,SAAS,EAAC,aAAa;cAAA6H,QAAA,gBAC1BlJ,OAAA;gBAAKqB,SAAS,EAAC,eAAe;gBAAA6H,QAAA,GAC3BlI,SAAS,KAAK,QAAQ,iBAAIhB,OAAA,CAACrC,OAAO;kBAAC0D,SAAS,EAAC;gBAAW;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC3D3I,SAAS,KAAK,aAAa,iBAAIhB,OAAA,CAACpC,SAAS;kBAACyD,SAAS,EAAC;gBAAW;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAClE3I,SAAS,KAAK,aAAa,iBAAIhB,OAAA,CAACpC,SAAS;kBAACyD,SAAS,EAAC;gBAAW;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAClE3I,SAAS,KAAK,OAAO,iBAAIhB,OAAA,CAACtC,MAAM;kBAAC2D,SAAS,EAAC;gBAAW;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1D3J,OAAA;kBAAMqB,SAAS,EAAC,YAAY;kBAAA6H,QAAA,EACzBlI,SAAS,KAAK,aAAa,GAAG,MAAM,GACpCA,SAAS,KAAK,aAAa,GAAG,YAAY,GAC1CA,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG;gBAAM;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN3J,OAAA;gBAAKqB,SAAS,EAAC,cAAc;gBAAA6H,QAAA,GAE1BlI,SAAS,KAAK,QAAQ,IAAIgE,QAAQ,CAAC+F,SAAS,iBAC3C/K,OAAA;kBAAKqB,SAAS,EAAC,YAAY;kBAAA6H,QAAA,EACxBlE,QAAQ,CAACgG,MAAM,gBACdhL,OAAA;oBAAMqB,SAAS,EAAC,sBAAsB;oBAAA6H,QAAA,GAAC,aAC1B,EAACxI,cAAc,KAAK,SAAS,GAAGsE,QAAQ,CAAC+F,SAAS,GAAI,QAAO/F,QAAQ,CAAC+F,SAAU,EAAC;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CAAC,GACL3E,QAAQ,CAACiG,eAAe,iBAC1BjL,OAAA;oBAAMqB,SAAS,EAAC,wBAAwB;oBAAA6H,QAAA,GAAC,cAC3B,EAACxI,cAAc,KAAK,SAAS,GAAI,SAAQsE,QAAQ,CAACiG,eAAgB,EAAC,GAAI,QAAOjG,QAAQ,CAACiG,eAAgB,EAAC;kBAAA;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChH;gBACP;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EACA3E,QAAQ,CAACE,IAAI,iBACZlF,OAAA;kBAAMqB,SAAS,EAAC,eAAe;kBAAA6H,QAAA,EAAElE,QAAQ,CAACE;gBAAI;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL3I,SAAS,KAAK,QAAQ,KAAKgE,QAAQ,CAACkC,QAAQ,IAAIlC,QAAQ,CAAC0D,OAAO,CAAC,iBAChE1I,OAAA;cAAKqB,SAAS,EAAC,2BAA2B;cAACkJ,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAACC,KAAK,CAAE;cAAAkC,QAAA,gBAC/ElJ,OAAA;gBACEkL,GAAG,EAAE1C,eAAe,CAACxD,QAAQ,CAAE;gBAC/BmG,GAAG,EAAEnG,QAAQ,CAACC,KAAM;gBACpB5D,SAAS,EAAC,iBAAiB;gBAC3B+J,OAAO,EAAGR,CAAC,IAAK;kBACd;kBACA,IAAI5F,QAAQ,CAAC0D,OAAO,IAAI,CAAC1D,QAAQ,CAAC0D,OAAO,CAAChF,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACnE;oBACA,IAAIiF,OAAO,GAAG3D,QAAQ,CAAC0D,OAAO;oBAC9B,IAAIC,OAAO,CAACjF,QAAQ,CAAC,aAAa,CAAC,IAAIiF,OAAO,CAACjF,QAAQ,CAAC,UAAU,CAAC,EAAE;sBACnE,MAAMkF,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;sBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;oBACtC;oBAEA,IAAI,CAACiC,CAAC,CAACC,MAAM,CAACK,GAAG,CAACxH,QAAQ,CAAC,aAAa,CAAC,EAAE;sBACzCkH,CAAC,CAACC,MAAM,CAACK,GAAG,GAAI,8BAA6BvC,OAAQ,oBAAmB;oBAC1E,CAAC,MAAM,IAAIiC,CAAC,CAACC,MAAM,CAACK,GAAG,CAACxH,QAAQ,CAAC,eAAe,CAAC,EAAE;sBACjDkH,CAAC,CAACC,MAAM,CAACK,GAAG,GAAI,8BAA6BvC,OAAQ,gBAAe;oBACtE,CAAC,MAAM,IAAIiC,CAAC,CAACC,MAAM,CAACK,GAAG,CAACxH,QAAQ,CAAC,WAAW,CAAC,EAAE;sBAC7CkH,CAAC,CAACC,MAAM,CAACK,GAAG,GAAI,8BAA6BvC,OAAQ,gBAAe;oBACtE,CAAC,MAAM;sBACL;sBACAiC,CAAC,CAACC,MAAM,CAACK,GAAG,GAAG,4cAA4c;oBAC7d;kBACF,CAAC,MAAM;oBACL;oBACAN,CAAC,CAACC,MAAM,CAACK,GAAG,GAAG,4cAA4c;kBAC7d;gBACF;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF3J,OAAA;gBAAKqB,SAAS,EAAC,cAAc;gBAAA6H,QAAA,gBAC3BlJ,OAAA,CAACvC,YAAY;kBAAC4D,SAAS,EAAC;gBAAW;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtC3J,OAAA;kBAAMqB,SAAS,EAAC,WAAW;kBAAA6H,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAED3J,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,gBAC3BlJ,OAAA;gBAAIqB,SAAS,EAAC,gBAAgB;gBAAA6H,QAAA,EAAElE,QAAQ,CAACC;cAAK;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpD3J,OAAA;gBAAKqB,SAAS,EAAC,eAAe;gBAAA6H,QAAA,gBAC5BlJ,OAAA;kBAAMqB,SAAS,EAAC,kBAAkB;kBAAA6H,QAAA,EAAElE,QAAQ,CAACd;gBAAO;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC3D3E,QAAQ,CAAC3D,SAAS,iBACjBrB,OAAA;kBAAMqB,SAAS,EAAC,gBAAgB;kBAAA6H,QAAA,EAC7BxI,cAAc,KAAK,SAAS,GAAI,SAAQsE,QAAQ,CAAC3D,SAAU,EAAC,GAAI,QAAO2D,QAAQ,CAAC3D,SAAU;gBAAC;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3J,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,EAC1BlI,SAAS,KAAK,QAAQ,KAAKgE,QAAQ,CAACkC,QAAQ,IAAIlC,QAAQ,CAAC0D,OAAO,CAAC,gBAChE1I,OAAA;gBAAKqB,SAAS,EAAC,iBAAiB;gBAAA6H,QAAA,eAC9BlJ,OAAA;kBAAMqB,SAAS,EAAC,gBAAgB;kBAAA6H,QAAA,EAAC;gBAAuB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,GACJ3E,QAAQ,CAAC7C,WAAW,gBACtBnC,OAAA,CAAAE,SAAA;gBAAAgJ,QAAA,gBACElJ,OAAA;kBACEqB,SAAS,EAAC,sBAAsB;kBAChCkJ,OAAO,EAAEA,CAAA,KAAMzD,qBAAqB,CAAC9B,QAAQ,CAAC7C,WAAW,CAAE;kBAAA+G,QAAA,gBAE3DlJ,OAAA,CAACjC,KAAK;oBAAAyL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SACX;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3J,OAAA;kBACEqB,SAAS,EAAC,oBAAoB;kBAC9BkJ,OAAO,EAAEA,CAAA,KAAM9E,sBAAsB,CAACT,QAAQ,CAAC7C,WAAW,CAAE;kBAAA+G,QAAA,gBAE5DlJ,OAAA,CAAClC,UAAU;oBAAA0L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,aAChB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT,CAAC,gBAEH3J,OAAA;gBAAMqB,SAAS,EAAC,aAAa;gBAAA6H,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAClD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GA7GE3C,KAAK;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8GV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN3J,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAA6H,QAAA,gBAC1BlJ,OAAA,CAACnC,eAAe;YAACwD,SAAS,EAAC;UAAY;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C3J,OAAA;YAAAkJ,QAAA,EAAI;UAAkB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B3J,OAAA;YAAAkJ,QAAA,EAAG;UAA4D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnE3J,OAAA;YAAGqB,SAAS,EAAC,YAAY;YAAA6H,QAAA,EAAC;UAA2C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL5H,gBAAgB,CAACyB,MAAM,GAAG,CAAC,IAAIb,iBAAiB,KAAK,IAAI,iBACxD3C,OAAA;QAAKqB,SAAS,EAAG,iBAAgBoB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAC8H,OAAO,EAAGK,CAAC,IAAK;UACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACS,aAAa,EAAE9D,eAAe,CAAC,CAAC;QACrD,CAAE;QAAA2B,QAAA,eACAlJ,OAAA;UAAKqB,SAAS,EAAG,eAAcoB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;UAAAyG,QAAA,EAChE,CAAC,MAAM;YACN,MAAMjC,KAAK,GAAGtC,0BAA0B,CAAChC,iBAAiB,CAAC;YAC3D,IAAI,CAACsE,KAAK,EAAE,oBAAOjH,OAAA;cAAAkJ,QAAA,EAAK;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;YAE7C,oBACE3J,OAAA,CAAAE,SAAA;cAAAgJ,QAAA,gBACElJ,OAAA;gBAAKqB,SAAS,EAAC,cAAc;gBAAA6H,QAAA,gBAC3BlJ,OAAA;kBAAKqB,SAAS,EAAC,YAAY;kBAAA6H,QAAA,gBACzBlJ,OAAA;oBAAAkJ,QAAA,EAAKjC,KAAK,CAAChC,KAAK,IAAI;kBAAgB;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1C3J,OAAA;oBAAKqB,SAAS,EAAC,YAAY;oBAAA6H,QAAA,gBACzBlJ,OAAA;sBAAMqB,SAAS,EAAC,eAAe;sBAAA6H,QAAA,EAAEjC,KAAK,CAAC/C,OAAO,IAAI;oBAAiB;sBAAAsF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3E3J,OAAA;sBAAMqB,SAAS,EAAC,aAAa;sBAAA6H,QAAA,GAAC,QAAM,EAACjC,KAAK,CAAC5F,SAAS,IAAI,KAAK;oBAAA;sBAAAmI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3J,OAAA;kBAAKqB,SAAS,EAAC,gBAAgB;kBAAA6H,QAAA,GAC5B,CAACzG,eAAe,gBACfzC,OAAA;oBACEqB,SAAS,EAAC,wBAAwB;oBAClCkJ,OAAO,EAAEzC,iBAAkB;oBAC3B7C,KAAK,EAAC,sBAAsB;oBAAAiE,QAAA,eAE5BlJ,OAAA,CAAC7B,QAAQ;sBAAAqL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAET3J,OAAA;oBACEqB,SAAS,EAAC,0BAA0B;oBACpCkJ,OAAO,EAAExC,mBAAoB;oBAC7B9C,KAAK,EAAC,iBAAiB;oBAAAiE,QAAA,eAEvBlJ,OAAA,CAAC5B,UAAU;sBAAAoL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACT,eACD3J,OAAA;oBACEqB,SAAS,EAAC,uBAAuB;oBACjCkJ,OAAO,EAAEhD,eAAgB;oBACzBtC,KAAK,EAAC,aAAa;oBAAAiE,QAAA,eAEnBlJ,OAAA,CAAChC,OAAO;sBAAAwL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3J,OAAA;gBAAKqB,SAAS,EAAC,iBAAiB;gBAAA6H,QAAA,EAC7BjC,KAAK,CAACC,QAAQ,gBACblH,OAAA;kBAAKsL,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAEC,YAAY,EAAE;kBAAM,CAAE;kBAAAvC,QAAA,gBACrElJ,OAAA;oBACE0L,GAAG,EAAGA,GAAG,IAAKxI,WAAW,CAACwI,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,KAAK;oBACZC,MAAM,EAAEzD,eAAe,CAACvB,KAAK,CAAE;oBAC/BqE,KAAK,EAAE;sBACLS,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,OAAO;sBACfE,eAAe,EAAE;oBACnB,CAAE;oBACFd,OAAO,EAAGR,CAAC,IAAK;sBACd9H,aAAa,CAAE,yBAAwBmE,KAAK,CAAChC,KAAM,mCAAkC,CAAC;oBACxF,CAAE;oBACFkH,SAAS,EAAEA,CAAA,KAAM;sBACfrJ,aAAa,CAAC,IAAI,CAAC;oBACrB,CAAE;oBACFsJ,gBAAgB,EAAEA,CAAA,KAAM;sBACtB;sBACA,IAAInF,KAAK,CAACoF,SAAS,IAAIpF,KAAK,CAACoF,SAAS,CAAC7I,MAAM,GAAG,CAAC,IAAIT,gBAAgB,KAAK,KAAK,EAAE;wBAC/E,MAAMuJ,eAAe,GAAGrF,KAAK,CAACoF,SAAS,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC,IAAIxF,KAAK,CAACoF,SAAS,CAAC,CAAC,CAAC;wBACxF7E,oBAAoB,CAAC8E,eAAe,CAAC7E,QAAQ,CAAC;sBAChD;oBACF,CAAE;oBACFiF,WAAW,EAAC,WAAW;oBAAAxD,QAAA,gBAGvBlJ,OAAA;sBAAQkL,GAAG,EAAEjE,KAAK,CAACI,cAAc,IAAIJ,KAAK,CAACC,QAAS;sBAACyF,IAAI,EAAC;oBAAW;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAGvE1C,KAAK,CAACoF,SAAS,IAAIpF,KAAK,CAACoF,SAAS,CAAC7I,MAAM,GAAG,CAAC,IAAIyD,KAAK,CAACoF,SAAS,CAACnC,GAAG,CAAC,CAAC0C,QAAQ,EAAE5F,KAAK,kBACpFhH,OAAA;sBAEE6M,IAAI,EAAC,WAAW;sBAChB3B,GAAG,EAAE0B,QAAQ,CAAC9G,GAAI;sBAClBgH,OAAO,EAAEF,QAAQ,CAACnF,QAAS;sBAC3BsC,KAAK,EAAE6C,QAAQ,CAACG,YAAa;sBAC7BC,OAAO,EAAEJ,QAAQ,CAACH,SAAS,IAAIzF,KAAK,KAAK;oBAAE,GALrC,GAAE4F,QAAQ,CAACnF,QAAS,IAAGT,KAAM,EAAC;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,eAEF3J,OAAA;sBAAGsL,KAAK,EAAE;wBAACrB,KAAK,EAAE,OAAO;wBAAEgD,SAAS,EAAE,QAAQ;wBAAE1B,OAAO,EAAE;sBAAM,CAAE;sBAAArC,QAAA,GAAC,8CAEhE,eAAAlJ,OAAA;wBAAAwJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN3J,OAAA;wBAAGoG,IAAI,EAAEa,KAAK,CAACI,cAAc,IAAIJ,KAAK,CAACC,QAAS;wBAAC2D,MAAM,EAAC,QAAQ;wBAACqC,GAAG,EAAC,qBAAqB;wBAAC5B,KAAK,EAAE;0BAACrB,KAAK,EAAE;wBAAS,CAAE;wBAAAf,QAAA,EAAC;sBAEtH;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,EAGP1C,KAAK,CAACoF,SAAS,IAAIpF,KAAK,CAACoF,SAAS,CAAC7I,MAAM,GAAG,CAAC,iBAC5CxD,OAAA;oBAAKsL,KAAK,EAAE;sBACVC,OAAO,EAAE,WAAW;sBACpBC,UAAU,EAAE,iBAAiB;sBAC7BC,YAAY,EAAE,aAAa;sBAC3B0B,SAAS,EAAE;oBACb,CAAE;oBAAAjE,QAAA,eACAlJ,OAAA;sBAAKsL,KAAK,EAAE;wBACV8B,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,GAAG,EAAE,MAAM;wBACXC,QAAQ,EAAE;sBACZ,CAAE;sBAAArE,QAAA,gBACAlJ,OAAA;wBAAMsL,KAAK,EAAE;0BACXrB,KAAK,EAAE,MAAM;0BACbuD,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,KAAK;0BACjBC,QAAQ,EAAE;wBACZ,CAAE;wBAAAxE,QAAA,EAAC;sBAEH;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAEP3J,OAAA;wBAAKsL,KAAK,EAAE;0BACV8B,OAAO,EAAE,MAAM;0BACfE,GAAG,EAAE,KAAK;0BACVC,QAAQ,EAAE,MAAM;0BAChBI,IAAI,EAAE;wBACR,CAAE;wBAAAzE,QAAA,gBAEAlJ,OAAA;0BACEuK,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAAC,KAAK,CAAE;0BAC3C8D,KAAK,EAAE;4BACLC,OAAO,EAAE,UAAU;4BACnBE,YAAY,EAAE,MAAM;4BACpBmC,MAAM,EAAE,MAAM;4BACdJ,QAAQ,EAAE,MAAM;4BAChBC,UAAU,EAAE,KAAK;4BACjBI,MAAM,EAAE,SAAS;4BACjBhE,UAAU,EAAE,eAAe;4BAC3BqC,eAAe,EAAEnJ,gBAAgB,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;4BACnEkH,KAAK,EAAE;0BACT,CAAE;0BAAAf,QAAA,EACH;wBAED;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAGR1C,KAAK,CAACoF,SAAS,CAACnC,GAAG,CAAE0C,QAAQ,iBAC5B5M,OAAA;0BAEEuK,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAACoF,QAAQ,CAACnF,QAAQ,CAAE;0BACvD6D,KAAK,EAAE;4BACLC,OAAO,EAAE,UAAU;4BACnBE,YAAY,EAAE,MAAM;4BACpBmC,MAAM,EAAE,MAAM;4BACdJ,QAAQ,EAAE,MAAM;4BAChBC,UAAU,EAAE,KAAK;4BACjBI,MAAM,EAAE,SAAS;4BACjBhE,UAAU,EAAE,eAAe;4BAC3BqC,eAAe,EAAEnJ,gBAAgB,KAAK6J,QAAQ,CAACnF,QAAQ,GAAG,SAAS,GAAG,SAAS;4BAC/EwC,KAAK,EAAE,MAAM;4BACbmD,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,GAAG,EAAE;0BACP,CAAE;0BAAApE,QAAA,gBAEFlJ,OAAA;4BAAAkJ,QAAA,EAAO0D,QAAQ,CAACG;0BAAY;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EACnCiD,QAAQ,CAACkB,eAAe,iBACvB9N,OAAA;4BAAMsL,KAAK,EAAE;8BACXkC,QAAQ,EAAE,MAAM;8BAChBnE,OAAO,EAAE,GAAG;8BACZ6C,eAAe,EAAE,uBAAuB;8BACxCX,OAAO,EAAE,SAAS;8BAClBE,YAAY,EAAE;4BAChB,CAAE;4BAAAvC,QAAA,EAAC;0BAEH;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP;wBAAA,GA5BIiD,QAAQ,CAACnF,QAAQ;0BAAA+B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA6BhB,CACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,GACN1C,KAAK,CAACyB,OAAO;gBAAA;gBACf;gBACA1I,OAAA;kBACEkL,GAAG,EAAG,iCAAgCjE,KAAK,CAACyB,OAAQ,mBAAmB;kBACvEzD,KAAK,EAAEgC,KAAK,CAAChC,KAAM;kBACnB8I,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACf3M,SAAS,EAAC,cAAc;kBACxB4M,MAAM,EAAEA,CAAA,KAAMpN,OAAO,CAACC,GAAG,CAAC,yBAAyB;gBAAE;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEV3J,OAAA;kBAAKqB,SAAS,EAAC,aAAa;kBAAA6H,QAAA,gBAC1BlJ,OAAA;oBAAKqB,SAAS,EAAC,YAAY;oBAAA6H,QAAA,EAAC;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpC3J,OAAA;oBAAAkJ,QAAA,EAAI;kBAAiB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1B3J,OAAA;oBAAAkJ,QAAA,EAAIrG,UAAU,IAAI;kBAA4C;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnE3J,OAAA;oBAAKqB,SAAS,EAAC,eAAe;oBAAA6H,QAAA,eAC5BlJ,OAAA;sBACEoG,IAAI,EAAEa,KAAK,CAACI,cAAc,IAAIJ,KAAK,CAACC,QAAS;sBAC7C2D,MAAM,EAAC,QAAQ;sBACfqC,GAAG,EAAC,qBAAqB;sBACzB7L,SAAS,EAAC,mBAAmB;sBAAA6H,QAAA,EAC9B;oBAED;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EACL,CAAClH,eAAe,iBACfzC,OAAA;gBAAKqB,SAAS,EAAC,cAAc;gBAAA6H,QAAA,eAC3BlJ,OAAA;kBAAKqB,SAAS,EAAC,mBAAmB;kBAAA6H,QAAA,gBAChClJ,OAAA;oBAAAkJ,QAAA,GAAG,mDAAiD,EAACjC,KAAK,CAAC/C,OAAO,EAAC,GAAC;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,EACvE1C,KAAK,CAACiH,wBAAwB,KAAK,YAAY,iBAC9ClO,OAAA;oBAAKqB,SAAS,EAAC,iBAAiB;oBAACiK,KAAK,EAAE;sBACtC6C,SAAS,EAAE,MAAM;sBACjBX,QAAQ,EAAE,OAAO;sBACjBvD,KAAK,EAAE,SAAS;sBAChBmD,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,GAAG,EAAE;oBACP,CAAE;oBAAApE,QAAA,gBACAlJ,OAAA;sBAAKsL,KAAK,EAAE;wBACVS,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACd4B,MAAM,EAAE,mBAAmB;wBAC3BT,SAAS,EAAE,uBAAuB;wBAClC1B,YAAY,EAAE,KAAK;wBACnB2C,SAAS,EAAE;sBACb;oBAAE;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,wCAEX;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,eACD,CAAC;UAEP,CAAC,EAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD3J,OAAA,CAACxC,QAAQ;QACPyE,WAAW,EAAEA,WAAY;QACzBoM,UAAU,EAAEA,CAAA,KAAM;UAChBnM,cAAc,CAAC,KAAK,CAAC;UACrBE,cAAc,CAAC,EAAE,CAAC;QACpB,CAAE;QACFD,WAAW,EAAEA;MAAY;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvJ,EAAA,CArhCQD,aAAa;EAAA,QACH9C,WAAW,EACXD,WAAW;AAAA;AAAAkR,EAAA,GAFrBnO,aAAa;AAuhCtB,eAAeA,aAAa;AAAC,IAAAmO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}