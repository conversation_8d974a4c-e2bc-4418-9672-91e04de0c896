import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { TbBrain } from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { QuizGrid } from '../../../components/modern';
import './responsive.css';
import './style.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    const getExams = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getAllExams();
        dispatch(HideLoading());

        if (response.success) {
          setExams(response.data);
        } else {
          message.error(response.message);
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
      }
    };

    getExams();
  }, [dispatch]);

  const handleQuizStart = (quiz) => {
    navigate(`/quiz/${quiz._id}/instructions`);
  };

  return (
    <div className="quiz-listing-container">
      <div className="quiz-listing-content">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="quiz-listing-header"
        >
          <div className="text-center mb-6">
            <h1 className="heading-2 text-gradient mb-4">
              <TbBrain className="inline w-10 h-10 mr-3" />
              Challenge Your Brain, Beat the Rest
            </h1>
          </div>
        </motion.div>

        {/* Quiz Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {exams.length > 0 ? (
            <QuizGrid
              quizzes={exams}
              onQuizStart={handleQuizStart}
              className="quiz-grid-container"
            />
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">
                No quizzes available at the moment.
              </div>
              <div className="text-gray-400 text-sm mt-2">
                Check back later for new challenges!
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Quiz;
