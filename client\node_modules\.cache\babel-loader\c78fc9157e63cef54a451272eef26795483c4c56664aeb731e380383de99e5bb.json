{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizStart.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlayerPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizStart = () => {\n  _s();\n  var _examData$questions, _user$name, _user$name$charAt;\n  const [examData, setExamData] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n  if (!examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(Loading, {\n        fullScreen: true,\n        text: \"Loading quiz details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-start-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-start-card\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"quiz-start-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6 backdrop-blur-sm\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-10 h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"quiz-start-title\",\n          children: examData.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"quiz-start-subtitle\",\n          children: \"Ready to challenge yourself? Let's test your knowledge!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"quiz-start-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-info-item\",\n          children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n            className: \"quiz-info-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-value\",\n            children: ((_examData$questions = examData.questions) === null || _examData$questions === void 0 ? void 0 : _examData$questions.length) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-label\",\n            children: \"Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-info-item\",\n          children: [/*#__PURE__*/_jsxDEV(TbClock, {\n            className: \"quiz-info-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-value\",\n            children: examData.duration || 30\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-label\",\n            children: \"Minutes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-info-item\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"quiz-info-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-value\",\n            children: [examData.passingPercentage || 70, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-info-label\",\n            children: \"Pass Mark\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"heading-3 mb-6 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n              className: \"w-6 h-6 text-warning-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), \" Instructions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 text-gray-700\",\n            children: [\"Read each question carefully before answering\", \"You can navigate between questions using Previous/Next buttons\", \"Make sure to answer all questions before submitting\", \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"].map((instruction, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: 20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                delay: 0.4 + index * 0.1\n              },\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-primary-600 font-bold text-sm\",\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"leading-relaxed\",\n                children: instruction\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-primary-600 font-bold text-lg\",\n                children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900\",\n                children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'Student', \"!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: [\"Level: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge-primary\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) || 'Primary'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 26\n                }, this), \" \\u2022 Class: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge-primary\",\n                  children: (user === null || user === void 0 ? void 0 : user.class) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 101\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.6\n        },\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          size: \"lg\",\n          onClick: () => navigate('/user/quiz'),\n          icon: /*#__PURE__*/_jsxDEV(TbArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 19\n          }, this),\n          className: \"sm:w-auto w-full\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"gradient\",\n          size: \"lg\",\n          onClick: handleStartQuiz,\n          icon: /*#__PURE__*/_jsxDEV(TbPlayerPlay, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 19\n          }, this),\n          iconPosition: \"right\",\n          className: \"sm:w-auto w-full\",\n          children: \"Start Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizStart, \"+k6XuQiozenodmM12ysuBxR6GPY=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizStart;\nexport default QuizStart;\nvar _c;\n$RefreshReg$(_c, \"QuizStart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "motion", "message", "getExamById", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Loading", "TbClock", "TbQuestionMark", "TbTrophy", "TbAlertTriangle", "TbPlayerPlay", "TbArrowLeft", "TbBrain", "jsxDEV", "_jsxDEV", "QuizStart", "_s", "_examData$questions", "_user$name", "_user$name$charAt", "examData", "setExamData", "id", "navigate", "dispatch", "user", "state", "fetchExamData", "response", "examId", "success", "data", "error", "document", "body", "classList", "add", "remove", "handleStartQuiz", "className", "children", "fullScreen", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "name", "transition", "delay", "questions", "length", "duration", "passingPercentage", "map", "instruction", "index", "x", "char<PERSON>t", "toUpperCase", "level", "class", "variant", "size", "onClick", "icon", "iconPosition", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizStart.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport {\n  TbClock,\n  TbQuestionMark,\n  TbTrophy,\n  TbAlertTriangle,\n  TbPlayerPlay,\n  TbArrowLeft,\n  TbBrain\n} from 'react-icons/tb';\nimport './responsive.css';\n\nconst QuizStart = () => {\n  const [examData, setExamData] = useState(null);\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n\n  if (!examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\">\n        <Loading fullScreen text=\"Loading quiz details...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"quiz-start-container\">\n      <div className=\"quiz-start-card\">\n        <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className=\"quiz-start-header\">\n          <div className=\"inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6 backdrop-blur-sm\">\n            <TbBrain className=\"w-10 h-10 text-white\" />\n          </div>\n          <h1 className=\"quiz-start-title\">{examData.name}</h1>\n          <p className=\"quiz-start-subtitle\">Ready to challenge yourself? Let's test your knowledge!</p>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }} className=\"quiz-start-info\">\n          <div className=\"quiz-info-item\">\n            <TbQuestionMark className=\"quiz-info-icon\" />\n            <div className=\"quiz-info-value\">{examData.questions?.length || 0}</div>\n            <div className=\"quiz-info-label\">Questions</div>\n          </div>\n          <div className=\"quiz-info-item\">\n            <TbClock className=\"quiz-info-icon\" />\n            <div className=\"quiz-info-value\">{examData.duration || 30}</div>\n            <div className=\"quiz-info-label\">Minutes</div>\n          </div>\n          <div className=\"quiz-info-item\">\n            <TbTrophy className=\"quiz-info-icon\" />\n            <div className=\"quiz-info-value\">{examData.passingPercentage || 70}%</div>\n            <div className=\"quiz-info-label\">Pass Mark</div>\n          </div>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>\n          <Card className=\"p-6 mb-6\">\n            <h2 className=\"heading-3 mb-6 flex items-center\">\n              <TbAlertTriangle className=\"w-6 h-6 text-warning-600 mr-2\" /> Instructions\n            </h2>\n            <div className=\"space-y-4 text-gray-700\">\n              {[\"Read each question carefully before answering\", \"You can navigate between questions using Previous/Next buttons\", \"Make sure to answer all questions before submitting\", \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"].map((instruction, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.4 + index * 0.1 }}\n                  className=\"flex items-start space-x-3\"\n                >\n                  <div className=\"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                    <span className=\"text-primary-600 font-bold text-sm\">{index + 1}</span>\n                  </div>\n                  <p className=\"leading-relaxed\">{instruction}</p>\n                </motion.div>\n              ))}\n            </div>\n          </Card>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>\n          <Card className=\"p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-600 font-bold text-lg\">\n                  {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n                </span>\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">Welcome, {user?.name || 'Student'}!</h3>\n                <p className=\"text-gray-600\">\n                  Level: <span className=\"badge-primary\">{user?.level || 'Primary'}</span> • Class: <span className=\"badge-primary\">{user?.class || 'N/A'}</span>\n                </p>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6 }} className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Button\n            variant=\"secondary\"\n            size=\"lg\"\n            onClick={() => navigate('/user/quiz')}\n            icon={<TbArrowLeft />}\n            className=\"sm:w-auto w-full\"\n          >\n            Back to Quizzes\n          </Button>\n          <Button\n            variant=\"gradient\"\n            size=\"lg\"\n            onClick={handleStartQuiz}\n            icon={<TbPlayerPlay />}\n            iconPosition=\"right\"\n            className=\"sm:w-auto w-full\"\n          >\n            Start Quiz\n          </Button>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizStart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAClE,SACEC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,eAAe,EACfC,YAAY,EACZC,WAAW,EACXC,OAAO,QACF,gBAAgB;AACvB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,UAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM;IAAE8B;EAAG,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAC1B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAK,CAAC,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnDhC,SAAS,CAAC,MAAM;IACd,MAAMkC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAM0B,QAAQ,GAAG,MAAM5B,WAAW,CAAC;UAAE6B,MAAM,EAAEP;QAAG,CAAC,CAAC;QAClDE,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI2B,QAAQ,CAACE,OAAO,EAAE;UACpBT,WAAW,CAACO,QAAQ,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLhC,OAAO,CAACiC,KAAK,CAACJ,QAAQ,CAAC7B,OAAO,CAAC;UAC/BwB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdR,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QACvBF,OAAO,CAACiC,KAAK,CAACA,KAAK,CAACjC,OAAO,CAAC;QAC5BwB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNK,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,EAAE,EAAEE,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE5B9B,SAAS,CAAC,MAAM;IACdwC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5Bf,QAAQ,CAAE,SAAQD,EAAG,OAAM,CAAC;EAC9B,CAAC;EAED,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEN,OAAA;MAAKyB,SAAS,EAAC,yFAAyF;MAAAC,QAAA,eACtG1B,OAAA,CAACT,OAAO;QAACoC,UAAU;QAACC,IAAI,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAKyB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACnC1B,OAAA;MAAKyB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1B,OAAA,CAAChB,MAAM,CAACiD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACX,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACvG1B,OAAA;UAAKyB,SAAS,EAAC,kGAAkG;UAAAC,QAAA,eAC/G1B,OAAA,CAACF,OAAO;YAAC2B,SAAS,EAAC;UAAsB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNhC,OAAA;UAAIyB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEpB,QAAQ,CAACgC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrDhC,OAAA;UAAGyB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAuD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,eAEbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAACf,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAChI1B,OAAA;UAAKyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1B,OAAA,CAACP,cAAc;YAACgC,SAAS,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7ChC,OAAA;YAAKyB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAE,EAAAvB,mBAAA,GAAAG,QAAQ,CAACmC,SAAS,cAAAtC,mBAAA,uBAAlBA,mBAAA,CAAoBuC,MAAM,KAAI;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxEhC,OAAA;YAAKyB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNhC,OAAA;UAAKyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1B,OAAA,CAACR,OAAO;YAACiC,SAAS,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtChC,OAAA;YAAKyB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAEpB,QAAQ,CAACqC,QAAQ,IAAI;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChEhC,OAAA;YAAKyB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNhC,OAAA;UAAKyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1B,OAAA,CAACN,QAAQ;YAAC+B,SAAS,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvChC,OAAA;YAAKyB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,GAAEpB,QAAQ,CAACsC,iBAAiB,IAAI,EAAE,EAAC,GAAC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1EhC,OAAA;YAAKyB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,eACpG1B,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACxB1B,OAAA;YAAIyB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC9C1B,OAAA,CAACL,eAAe;cAAC8B,SAAS,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAC/D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhC,OAAA;YAAKyB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EACrC,CAAC,+CAA+C,EAAE,gEAAgE,EAAE,qDAAqD,EAAE,yEAAyE,CAAC,CAACmB,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAC5Q/C,OAAA,CAAChB,MAAM,CAACiD,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAG,CAAE;cAC/BX,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAE,CAAE;cAC9BT,UAAU,EAAE;gBAAEC,KAAK,EAAE,GAAG,GAAGO,KAAK,GAAG;cAAI,CAAE;cACzCtB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAEtC1B,OAAA;gBAAKyB,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,eACxG1B,OAAA;kBAAMyB,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAEqB,KAAK,GAAG;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNhC,OAAA;gBAAGyB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEoB;cAAW;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAT3Ce,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,eACpG1B,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,yEAAyE;UAAAC,QAAA,eACvF1B,OAAA;YAAKyB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1B,OAAA;cAAKyB,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF1B,OAAA;gBAAMyB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EACjD,CAAAf,IAAI,aAAJA,IAAI,wBAAAP,UAAA,GAAJO,IAAI,CAAE2B,IAAI,cAAAlC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY6C,MAAM,CAAC,CAAC,CAAC,cAAA5C,iBAAA,uBAArBA,iBAAA,CAAuB6C,WAAW,CAAC,CAAC,KAAI;cAAG;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhC,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAIyB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAC,WAAS,EAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,KAAI,SAAS,EAAC,GAAC;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFhC,OAAA;gBAAGyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,SACpB,eAAA1B,OAAA;kBAAMyB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,KAAK,KAAI;gBAAS;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,mBAAU,eAAAhC,OAAA;kBAAMyB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,KAAK,KAAI;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;QAACC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAACC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAACG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAACf,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC/J1B,OAAA,CAACV,MAAM;UACL+D,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAM9C,QAAQ,CAAC,YAAY,CAAE;UACtC+C,IAAI,eAAExD,OAAA,CAACH,WAAW;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThC,OAAA,CAACV,MAAM;UACL+D,OAAO,EAAC,UAAU;UAClBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAE/B,eAAgB;UACzBgC,IAAI,eAAExD,OAAA,CAACJ,YAAY;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvByB,YAAY,EAAC,OAAO;UACpBhC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAlJID,SAAS;EAAA,QAEErB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA2E,EAAA,GALxBzD,SAAS;AAoJf,eAAeA,SAAS;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}