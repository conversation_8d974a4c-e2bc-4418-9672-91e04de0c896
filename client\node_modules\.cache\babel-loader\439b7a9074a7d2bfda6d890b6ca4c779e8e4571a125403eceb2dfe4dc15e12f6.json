{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizStart.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlayerPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizStart = () => {\n  _s();\n  var _examData$questions, _user$name, _user$name$charAt;\n  const [examData, setExamData] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n  if (!examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(Loading, {\n        fullScreen: true,\n        text: \"Loading quiz details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-start-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-start-card\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"quiz-start-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6 backdrop-blur-sm\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-10 h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"quiz-start-title\",\n          children: examData.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"quiz-start-subtitle\",\n          children: \"Ready to challenge yourself? Let's test your knowledge!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-8 mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid md:grid-cols-2 gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"heading-3 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                  className: \"w-6 h-6 text-primary-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this), \"Quiz Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.3\n                  },\n                  className: \"flex items-center justify-between p-4 bg-primary-50 rounded-lg border border-primary-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                      className: \"w-5 h-5 text-primary-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Questions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-primary-600\",\n                    children: ((_examData$questions = examData.questions) === null || _examData$questions === void 0 ? void 0 : _examData$questions.length) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.4\n                  },\n                  className: \"flex items-center justify-between p-4 bg-success-50 rounded-lg border border-success-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                      className: \"w-5 h-5 text-success-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Duration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-success-600\",\n                    children: [Math.floor(examData.duration / 60), \" minutes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.5\n                  },\n                  className: \"flex items-center justify-between p-4 bg-warning-50 rounded-lg border border-warning-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                      className: \"w-5 h-5 text-warning-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Total Marks\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-warning-600\",\n                    children: examData.totalMarks\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.6\n                  },\n                  className: \"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                      className: \"w-5 h-5 text-blue-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Passing Marks\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-blue-600\",\n                    children: examData.passingMarks\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"heading-3 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                  className: \"w-6 h-6 text-warning-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), \"Instructions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4 text-gray-700\",\n                children: [\"Read each question carefully before answering\", \"You can navigate between questions using Previous/Next buttons\", \"Make sure to answer all questions before submitting\", \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"].map((instruction, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.3 + index * 0.1\n                  },\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-primary-600 font-bold text-sm\",\n                      children: index + 1\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"leading-relaxed\",\n                    children: instruction\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-primary-600 font-bold text-lg\",\n                children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900\",\n                children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'Student', \"!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: [\"Level: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge-primary\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) || 'Primary'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 26\n                }, this), \" \\u2022 Class: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge-primary\",\n                  children: (user === null || user === void 0 ? void 0 : user.class) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 26\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.6\n        },\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          size: \"lg\",\n          onClick: () => navigate('/user/quiz'),\n          icon: /*#__PURE__*/_jsxDEV(TbArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 19\n          }, this),\n          className: \"sm:w-auto w-full\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"gradient\",\n          size: \"lg\",\n          onClick: handleStartQuiz,\n          icon: /*#__PURE__*/_jsxDEV(TbPlayerPlay, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 19\n          }, this),\n          iconPosition: \"right\",\n          className: \"sm:w-auto w-full\",\n          children: \"Start Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizStart, \"+k6XuQiozenodmM12ysuBxR6GPY=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizStart;\nexport default QuizStart;\nvar _c;\n$RefreshReg$(_c, \"QuizStart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "motion", "message", "getExamById", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Loading", "TbClock", "TbQuestionMark", "TbTrophy", "TbAlertTriangle", "TbPlayerPlay", "TbArrowLeft", "TbBrain", "jsxDEV", "_jsxDEV", "QuizStart", "_s", "_examData$questions", "_user$name", "_user$name$charAt", "examData", "setExamData", "id", "navigate", "dispatch", "user", "state", "fetchExamData", "response", "examId", "success", "data", "error", "document", "body", "classList", "add", "remove", "handleStartQuiz", "className", "children", "fullScreen", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "name", "transition", "delay", "x", "questions", "length", "Math", "floor", "duration", "totalMarks", "passingMarks", "map", "instruction", "index", "char<PERSON>t", "toUpperCase", "level", "class", "variant", "size", "onClick", "icon", "iconPosition", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizStart.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlayerPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\nimport './responsive.css';\n\nconst QuizStart = () => {\n  const [examData, setExamData] = useState(null);\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n        \n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n\n  if (!examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\">\n        <Loading fullScreen text=\"Loading quiz details...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"quiz-start-container\">\n      <div className=\"quiz-start-card\">\n        {/* Modern Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"quiz-start-header\"\n        >\n          <div className=\"inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6 backdrop-blur-sm\">\n            <TbBrain className=\"w-10 h-10 text-white\" />\n          </div>\n          <h1 className=\"quiz-start-title\">\n            {examData.name}\n          </h1>\n          <p className=\"quiz-start-subtitle\">\n            Ready to challenge yourself? Let's test your knowledge!\n          </p>\n        </motion.div>\n\n        {/* Modern Quiz Info Card */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <Card className=\"p-8 mb-8\">\n            <div className=\"grid md:grid-cols-2 gap-8\">\n              {/* Quiz Details */}\n              <div>\n                <h2 className=\"heading-3 mb-6 flex items-center\">\n                  <TbQuestionMark className=\"w-6 h-6 text-primary-600 mr-2\" />\n                  Quiz Details\n                </h2>\n                <div className=\"space-y-4\">\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.3 }}\n                    className=\"flex items-center justify-between p-4 bg-primary-50 rounded-lg border border-primary-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbQuestionMark className=\"w-5 h-5 text-primary-600\" />\n                      <span className=\"font-medium text-gray-700\">Questions</span>\n                    </div>\n                    <span className=\"font-bold text-primary-600\">{examData.questions?.length || 0}</span>\n                  </motion.div>\n\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.4 }}\n                    className=\"flex items-center justify-between p-4 bg-success-50 rounded-lg border border-success-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbClock className=\"w-5 h-5 text-success-600\" />\n                      <span className=\"font-medium text-gray-700\">Duration</span>\n                    </div>\n                    <span className=\"font-bold text-success-600\">{Math.floor(examData.duration / 60)} minutes</span>\n                  </motion.div>\n\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.5 }}\n                    className=\"flex items-center justify-between p-4 bg-warning-50 rounded-lg border border-warning-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbTrophy className=\"w-5 h-5 text-warning-600\" />\n                      <span className=\"font-medium text-gray-700\">Total Marks</span>\n                    </div>\n                    <span className=\"font-bold text-warning-600\">{examData.totalMarks}</span>\n                  </motion.div>\n\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.6 }}\n                    className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbTrophy className=\"w-5 h-5 text-blue-600\" />\n                      <span className=\"font-medium text-gray-700\">Passing Marks</span>\n                    </div>\n                    <span className=\"font-bold text-blue-600\">{examData.passingMarks}</span>\n                  </motion.div>\n                </div>\n              </div>\n\n              {/* Instructions */}\n              <div>\n                <h2 className=\"heading-3 mb-6 flex items-center\">\n                  <TbAlertTriangle className=\"w-6 h-6 text-warning-600 mr-2\" />\n                  Instructions\n                </h2>\n                <div className=\"space-y-4 text-gray-700\">\n                  {[\n                    \"Read each question carefully before answering\",\n                    \"You can navigate between questions using Previous/Next buttons\",\n                    \"Make sure to answer all questions before submitting\",\n                    \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"\n                  ].map((instruction, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: 0.3 + index * 0.1 }}\n                      className=\"flex items-start space-x-3\"\n                    >\n                      <div className=\"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                        <span className=\"text-primary-600 font-bold text-sm\">{index + 1}</span>\n                      </div>\n                      <p className=\"leading-relaxed\">{instruction}</p>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n\n        {/* Modern User Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <Card className=\"p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-600 font-bold text-lg\">\n                  {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n                </span>\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">Welcome, {user?.name || 'Student'}!</h3>\n                <p className=\"text-gray-600\">\n                  Level: <span className=\"badge-primary\">{user?.level || 'Primary'}</span> •\n                  Class: <span className=\"badge-primary\">{user?.class || 'N/A'}</span>\n                </p>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n\n        {/* Modern Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <Button\n            variant=\"secondary\"\n            size=\"lg\"\n            onClick={() => navigate('/user/quiz')}\n            icon={<TbArrowLeft />}\n            className=\"sm:w-auto w-full\"\n          >\n            Back to Quizzes\n          </Button>\n          <Button\n            variant=\"gradient\"\n            size=\"lg\"\n            onClick={handleStartQuiz}\n            icon={<TbPlayerPlay />}\n            iconPosition=\"right\"\n            className=\"sm:w-auto w-full\"\n          >\n            Start Quiz\n          </Button>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizStart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAClE,SAASC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,YAAY,EAAEC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AACvH,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,UAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM;IAAE8B;EAAG,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAC1B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAK,CAAC,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnDhC,SAAS,CAAC,MAAM;IACd,MAAMkC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAM0B,QAAQ,GAAG,MAAM5B,WAAW,CAAC;UAAE6B,MAAM,EAAEP;QAAG,CAAC,CAAC;QAClDE,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI2B,QAAQ,CAACE,OAAO,EAAE;UACpBT,WAAW,CAACO,QAAQ,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLhC,OAAO,CAACiC,KAAK,CAACJ,QAAQ,CAAC7B,OAAO,CAAC;UAC/BwB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdR,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QACvBF,OAAO,CAACiC,KAAK,CAACA,KAAK,CAACjC,OAAO,CAAC;QAC5BwB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNK,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,EAAE,EAAEE,QAAQ,EAAED,QAAQ,CAAC,CAAC;;EAE5B;EACA9B,SAAS,CAAC,MAAM;IACdwC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5Bf,QAAQ,CAAE,SAAQD,EAAG,OAAM,CAAC;EAC9B,CAAC;EAED,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEN,OAAA;MAAKyB,SAAS,EAAC,yFAAyF;MAAAC,QAAA,eACtG1B,OAAA,CAACT,OAAO;QAACoC,UAAU;QAACC,IAAI,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAKyB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACnC1B,OAAA;MAAKyB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAE9B1B,OAAA,CAAChB,MAAM,CAACiD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BX,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7B1B,OAAA;UAAKyB,SAAS,EAAC,kGAAkG;UAAAC,QAAA,eAC/G1B,OAAA,CAACF,OAAO;YAAC2B,SAAS,EAAC;UAAsB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNhC,OAAA;UAAIyB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7BpB,QAAQ,CAACgC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACLhC,OAAA;UAAGyB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAEnC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,eAE3B1B,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,UAAU;UAAAC,QAAA,eACxB1B,OAAA;YAAKyB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBAExC1B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAIyB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC9C1B,OAAA,CAACP,cAAc;kBAACgC,SAAS,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAKyB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1B,OAAA,CAAChB,MAAM,CAACiD,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,0FAA0F;kBAAAC,QAAA,gBAEpG1B,OAAA;oBAAKyB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C1B,OAAA,CAACP,cAAc;sBAACgC,SAAS,EAAC;oBAA0B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvDhC,OAAA;sBAAMyB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACNhC,OAAA;oBAAMyB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,EAAAvB,mBAAA,GAAAG,QAAQ,CAACoC,SAAS,cAAAvC,mBAAA,uBAAlBA,mBAAA,CAAoBwC,MAAM,KAAI;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAEbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,0FAA0F;kBAAAC,QAAA,gBAEpG1B,OAAA;oBAAKyB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C1B,OAAA,CAACR,OAAO;sBAACiC,SAAS,EAAC;oBAA0B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChDhC,OAAA;sBAAMyB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACNhC,OAAA;oBAAMyB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAEkB,IAAI,CAACC,KAAK,CAACvC,QAAQ,CAACwC,QAAQ,GAAG,EAAE,CAAC,EAAC,UAAQ;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,eAEbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,0FAA0F;kBAAAC,QAAA,gBAEpG1B,OAAA;oBAAKyB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C1B,OAAA,CAACN,QAAQ;sBAAC+B,SAAS,EAAC;oBAA0B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjDhC,OAAA;sBAAMyB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACNhC,OAAA;oBAAMyB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEpB,QAAQ,CAACyC;kBAAU;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC,eAEbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,gBAE9F1B,OAAA;oBAAKyB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C1B,OAAA,CAACN,QAAQ;sBAAC+B,SAAS,EAAC;oBAAuB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9ChC,OAAA;sBAAMyB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNhC,OAAA;oBAAMyB,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAEpB,QAAQ,CAAC0C;kBAAY;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhC,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAIyB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC9C1B,OAAA,CAACL,eAAe;kBAAC8B,SAAS,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAKyB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACrC,CACC,+CAA+C,EAC/C,gEAAgE,EAChE,qDAAqD,EACrD,yEAAyE,CAC1E,CAACuB,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACvBnD,OAAA,CAAChB,MAAM,CAACiD,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAG,CAAE;kBAC/BJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE,GAAG,GAAGW,KAAK,GAAG;kBAAI,CAAE;kBACzC1B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAEtC1B,OAAA;oBAAKyB,SAAS,EAAC,2FAA2F;oBAAAC,QAAA,eACxG1B,OAAA;sBAAMyB,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAEyB,KAAK,GAAG;oBAAC;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACNhC,OAAA;oBAAGyB,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAEwB;kBAAW;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAT3CmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUA,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,eAE3B1B,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,yEAAyE;UAAAC,QAAA,eACvF1B,OAAA;YAAKyB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1B,OAAA;cAAKyB,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF1B,OAAA;gBAAMyB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EACjD,CAAAf,IAAI,aAAJA,IAAI,wBAAAP,UAAA,GAAJO,IAAI,CAAE2B,IAAI,cAAAlC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYgD,MAAM,CAAC,CAAC,CAAC,cAAA/C,iBAAA,uBAArBA,iBAAA,CAAuBgD,WAAW,CAAC,CAAC,KAAI;cAAG;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhC,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAIyB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAC,WAAS,EAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,KAAI,SAAS,EAAC,GAAC;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFhC,OAAA;gBAAGyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,SACpB,eAAA1B,OAAA;kBAAMyB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2C,KAAK,KAAI;gBAAS;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,mBACjE,eAAAhC,OAAA;kBAAMyB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,KAAK,KAAI;gBAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbhC,OAAA,CAAChB,MAAM,CAACiD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3Bf,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE1D1B,OAAA,CAACV,MAAM;UACLkE,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,YAAY,CAAE;UACtCkD,IAAI,eAAE3D,OAAA,CAACH,WAAW;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThC,OAAA,CAACV,MAAM;UACLkE,OAAO,EAAC,UAAU;UAClBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAElC,eAAgB;UACzBmC,IAAI,eAAE3D,OAAA,CAACJ,YAAY;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB4B,YAAY,EAAC,OAAO;UACpBnC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CApOID,SAAS;EAAA,QAEErB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA8E,EAAA,GALxB5D,SAAS;AAsOf,eAAeA,SAAS;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}