{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbSchool, TbMenu2, TbX, TbMoon, TbSun } from \"react-icons/tb\";\nimport { Rate, message } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReviews } from \"../../../apicalls/reviews\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport Image2 from \"../../../assets/collage-2.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport { useTheme } from \"../../../contexts/ThemeContext\";\nimport { Button } from \"../../../components/modern\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const aboutUsSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [reviews, setReviews] = useState([]);\n  const dispatch = useDispatch();\n  const [menuOpen, setMenuOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  useEffect(() => {\n    getReviews();\n  }, []);\n  const getReviews = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getAllReviews();\n      if (response.success) {\n        setReviews(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  const scrollToSection = (ref, offset = 30) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home\",\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-header fixed w-full top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8\",\n              style: {\n                color: '#007BFF'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: [\"Brain\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"logo-accent\",\n                children: \"Wave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 22\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(homeSectionRef),\n              className: \"nav-item\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(aboutUsSectionRef),\n              className: \"nav-item\",\n              children: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(reviewsSectionRef),\n              className: \"nav-item\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactUsRef),\n              className: \"nav-item\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleTheme,\n              className: \"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n              children: isDarkMode ? /*#__PURE__*/_jsxDEV(TbSun, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 31\n              }, this) : /*#__PURE__*/_jsxDEV(TbMoon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 63\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  children: \"Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setMenuOpen(!menuOpen),\n              className: \"md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n              children: menuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 29\n              }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 59\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), menuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          className: \"md:hidden py-4 border-t border-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(homeSectionRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(aboutUsSectionRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(reviewsSectionRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(contactUsRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col space-y-2 pt-4 border-t border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary w-full\",\n                  children: \"Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary w-full\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: homeSectionRef,\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-grid\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  duration: 0.6,\n                  delay: 0.2\n                },\n                className: \"inline-flex items-center px-4 py-2 bg-primary-50 text-primary-700 rounded-full text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), \"#1 Educational Platform in Tanzania\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"hero-title\",\n                children: [\"Fueling Bright Futures with\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gradient\",\n                  children: [/*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                    className: \"inline w-12 h-12 lg:w-16 lg:h-16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this), \"Education.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-gray-600 dark:text-gray-300 leading-relaxed\",\n                children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"cta-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary\",\n                  children: \"Get Started Free\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"trust-indicators\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  className: \"w-5 h-5 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-5 h-5 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"hero-image\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: Image1,\n                alt: \"Students Learning\",\n                className: \"w-full h-auto rounded-2xl shadow-large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                className: \"absolute -top-4 -left-4 bg-white rounded-xl shadow-medium p-4\",\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  className: \"w-8 h-8 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                className: \"absolute -bottom-4 -right-4 bg-white rounded-xl shadow-medium p-4\",\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-8 h-8 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-br from-primary-100 to-blue-100 rounded-2xl transform rotate-3 scale-105 -z-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"C3M7iVoaLqGwl7U81FNJ6sVHVHo=\", false, function () {\n  return [useDispatch, useTheme];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbSchool", "TbMenu2", "TbX", "TbMoon", "TbSun", "Rate", "message", "useDispatch", "HideLoading", "ShowLoading", "getAllReviews", "Image1", "Image2", "contactUs", "useTheme", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Home", "_s", "homeSectionRef", "aboutUsSectionRef", "reviewsSectionRef", "contactUsRef", "reviews", "setReviews", "dispatch", "menuOpen", "setMenuOpen", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "isDarkMode", "toggleTheme", "getReviews", "response", "success", "data", "error", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "className", "children", "nav", "initial", "y", "opacity", "animate", "to", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "exit", "x", "transition", "duration", "delay", "src", "alt", "repeat", "Infinity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool,\r\n  TbMenu2,\r\n  TbX,\r\n  TbMoon,\r\n  TbSun\r\n} from \"react-icons/tb\";\r\nimport { Rate, message } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReviews } from \"../../../apicalls/reviews\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport Image2 from \"../../../assets/collage-2.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport { useTheme } from \"../../../contexts/ThemeContext\";\r\nimport { Button } from \"../../../components/modern\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const aboutUsSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [reviews, setReviews] = useState([]);\r\n  const dispatch = useDispatch();\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  useEffect(() => { getReviews(); }, []);\r\n\r\n  const getReviews = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getAllReviews();\r\n      if (response.success) {\r\n        setReviews(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const scrollToSection = (ref, offset = 30) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      {/* Navigation */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-header fixed w-full top-0 z-50\"\r\n      >\r\n        <div className=\"container\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Logo */}\r\n            <Link to=\"/\" className=\"flex items-center space-x-2\">\r\n              <TbBrain className=\"w-8 h-8\" style={{color: '#007BFF'}} />\r\n              <span className=\"logo-text\">\r\n                Brain<span className=\"logo-accent\">Wave</span>\r\n              </span>\r\n            </Link>\r\n\r\n            {/* Desktop Navigation */}\r\n            <div className=\"hidden md:flex items-center space-x-8\">\r\n              <button onClick={() => scrollToSection(homeSectionRef)} className=\"nav-item\">Home</button>\r\n              <button onClick={() => scrollToSection(aboutUsSectionRef)} className=\"nav-item\">About Us</button>\r\n              <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item\">Reviews</button>\r\n              <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item\">Contact Us</button>\r\n            </div>\r\n\r\n            {/* Right Section */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Theme Toggle */}\r\n              <button\r\n                onClick={toggleTheme}\r\n                className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {isDarkMode ? <TbSun className=\"w-5 h-5\" /> : <TbMoon className=\"w-5 h-5\" />}\r\n              </button>\r\n\r\n              {/* Auth Buttons - Desktop */}\r\n              <div className=\"hidden md:flex items-center space-x-3\">\r\n                <Link to=\"/login\">\r\n                  <button className=\"btn btn-secondary\">Login</button>\r\n                </Link>\r\n                <Link to=\"/register\">\r\n                  <button className=\"btn btn-primary\">Sign Up</button>\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                onClick={() => setMenuOpen(!menuOpen)}\r\n                className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {menuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          {menuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              className=\"md:hidden py-4 border-t border-gray-100\"\r\n            >\r\n              <div className=\"flex flex-col space-y-2\">\r\n                <button onClick={() => { scrollToSection(homeSectionRef); setMenuOpen(false); }} className=\"nav-item text-left\">Home</button>\r\n                <button onClick={() => { scrollToSection(aboutUsSectionRef); setMenuOpen(false); }} className=\"nav-item text-left\">About Us</button>\r\n                <button onClick={() => { scrollToSection(reviewsSectionRef); setMenuOpen(false); }} className=\"nav-item text-left\">Reviews</button>\r\n                <button onClick={() => { scrollToSection(contactUsRef); setMenuOpen(false); }} className=\"nav-item text-left\">Contact Us</button>\r\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-gray-100\">\r\n                  <Link to=\"/login\" className=\"flex-1\">\r\n                    <button className=\"btn btn-secondary w-full\">Login</button>\r\n                  </Link>\r\n                  <Link to=\"/register\" className=\"flex-1\">\r\n                    <button className=\"btn btn-primary w-full\">Sign Up</button>\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n\r\n      {/* Hero Section */}\r\n      <section ref={homeSectionRef} className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-grid\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"space-y-8\"\r\n            >\r\n              <div className=\"space-y-4\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"inline-flex items-center px-4 py-2 bg-primary-50 text-primary-700 rounded-full text-sm font-medium\"\r\n                >\r\n                  <TbSchool className=\"w-4 h-4 mr-2\" />\r\n                  #1 Educational Platform in Tanzania\r\n                </motion.div>\r\n\r\n                <h1 className=\"hero-title\">\r\n                  Fueling Bright Futures with{\" \"}\r\n                  <span className=\"text-gradient\">\r\n                    <TbArrowBigRightLinesFilled className=\"inline w-12 h-12 lg:w-16 lg:h-16\" />\r\n                    Education.\r\n                  </span>\r\n                </h1>\r\n\r\n                <p className=\"text-xl text-gray-600 dark:text-gray-300 leading-relaxed\">\r\n                  Discover limitless learning opportunities with our comprehensive\r\n                  online study platform. Study anywhere, anytime, and achieve your\r\n                  academic goals with confidence.\r\n                </p>\r\n              </div>\r\n\r\n              {/* CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"cta-buttons\"\r\n              >\r\n                <Link to=\"/register\">\r\n                  <button className=\"btn btn-primary\">\r\n                    Get Started Free\r\n                  </button>\r\n                </Link>\r\n                <Link to=\"/login\">\r\n                  <button className=\"btn btn-secondary\">\r\n                    Sign In\r\n                  </button>\r\n                </Link>\r\n              </motion.div>\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"trust-indicators\"\r\n              >\r\n                <div>\r\n                  <TbUsers className=\"w-5 h-5 text-primary-600\" />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div>\r\n                  <TbStar className=\"w-5 h-5 text-yellow-500\" />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div>\r\n                  <TbTrophy className=\"w-5 h-5 text-primary-600\" />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"hero-image\"\r\n            >\r\n              <div className=\"relative z-10\">\r\n                <img\r\n                  src={Image1}\r\n                  alt=\"Students Learning\"\r\n                  className=\"w-full h-auto rounded-2xl shadow-large\"\r\n                />\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"absolute -top-4 -left-4 bg-white rounded-xl shadow-medium p-4\"\r\n                >\r\n                  <TbBook className=\"w-8 h-8 text-primary-600\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"absolute -bottom-4 -right-4 bg-white rounded-xl shadow-medium p-4\"\r\n                >\r\n                  <TbTrophy className=\"w-8 h-8 text-yellow-500\" />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Background Decoration */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-primary-100 to-blue-100 rounded-2xl transform rotate-3 scale-105 -z-10\"></div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,KAAK,QACA,gBAAgB;AACvB,SAASC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,MAAM,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,iBAAiB,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM+B,iBAAiB,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMgC,YAAY,GAAGhC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMqC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC;IAAE0C,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAE1B,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM;IAAEgD,UAAU;IAAEC;EAAY,CAAC,GAAGxB,QAAQ,CAAC,CAAC;EAE9CxB,SAAS,CAAC,MAAM;IAAEiD,UAAU,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;EAEtC,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7Bb,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM+B,QAAQ,GAAG,MAAM9B,aAAa,CAAC,CAAC;MACtC,IAAI8B,QAAQ,CAACC,OAAO,EAAE;QACpBhB,UAAU,CAACe,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLpC,OAAO,CAACqC,KAAK,CAACH,QAAQ,CAAClC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAACA,KAAK,CAACrC,OAAO,CAAC;IAC9B;IACAoB,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMoC,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAExB,IAAI;MAAEyB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC3B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGyB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBzB,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAMM,IAAI,GAAG,MAAM7B,SAAS,CAACgB,QAAQ,CAAC;MACtC,IAAIa,IAAI,CAACD,OAAO,EAAE;QAChBnC,OAAO,CAACmC,OAAO,CAAC,4BAA4B,CAAC;QAC7CL,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAE1B,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACL8B,kBAAkB,CAACM,IAAI,CAACpC,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdP,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEjB,OAAA;IAAK2C,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEnB5C,OAAA,CAACxB,MAAM,CAACqE,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eAE9C5C,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5C,OAAA;UAAK2C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD5C,OAAA,CAACzB,IAAI;YAAC2E,EAAE,EAAC,GAAG;YAACP,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAClD5C,OAAA,CAACtB,OAAO;cAACiE,SAAS,EAAC,SAAS;cAACQ,KAAK,EAAE;gBAACC,KAAK,EAAE;cAAS;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DxD,OAAA;cAAM2C,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,OACrB,eAAA5C,OAAA;gBAAM2C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPxD,OAAA;YAAK2C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5C,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACxB,cAAc,CAAE;cAACwC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1FxD,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACvB,iBAAiB,CAAE;cAACuC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjGxD,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACtB,iBAAiB,CAAE;cAACsC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChGxD,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACrB,YAAY,CAAE;cAACqC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eAGNxD,OAAA;YAAK2C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1C5C,OAAA;cACEyD,OAAO,EAAEpC,WAAY;cACrBsB,SAAS,EAAC,2FAA2F;cAAAC,QAAA,EAEpGxB,UAAU,gBAAGpB,OAAA,CAACb,KAAK;gBAACwD,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxD,OAAA,CAACd,MAAM;gBAACyD,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAGTxD,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5C,OAAA,CAACzB,IAAI;gBAAC2E,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACf5C,OAAA;kBAAQ2C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACPxD,OAAA,CAACzB,IAAI;gBAAC2E,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClB5C,OAAA;kBAAQ2C,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNxD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAM9C,WAAW,CAAC,CAACD,QAAQ,CAAE;cACtCiC,SAAS,EAAC,qGAAqG;cAAAC,QAAA,EAE9GlC,QAAQ,gBAAGV,OAAA,CAACf,GAAG;gBAAC0D,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxD,OAAA,CAAChB,OAAO;gBAAC2D,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL9C,QAAQ,iBACPV,OAAA,CAACxB,MAAM,CAACkF,GAAG;UACTZ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BY,IAAI,EAAE;YAAEX,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eAEnD5C,OAAA;YAAK2C,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC5C,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM;gBAAE9B,eAAe,CAACxB,cAAc,CAAC;gBAAEQ,WAAW,CAAC,KAAK,CAAC;cAAE,CAAE;cAACgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7HxD,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM;gBAAE9B,eAAe,CAACvB,iBAAiB,CAAC;gBAAEO,WAAW,CAAC,KAAK,CAAC;cAAE,CAAE;cAACgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpIxD,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM;gBAAE9B,eAAe,CAACtB,iBAAiB,CAAC;gBAAEM,WAAW,CAAC,KAAK,CAAC;cAAE,CAAE;cAACgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnIxD,OAAA;cAAQyD,OAAO,EAAEA,CAAA,KAAM;gBAAE9B,eAAe,CAACrB,YAAY,CAAC;gBAAEK,WAAW,CAAC,KAAK,CAAC;cAAE,CAAE;cAACgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjIxD,OAAA;cAAK2C,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACpE5C,OAAA,CAACzB,IAAI;gBAAC2E,EAAE,EAAC,QAAQ;gBAACP,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eAClC5C,OAAA;kBAAQ2C,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACPxD,OAAA,CAACzB,IAAI;gBAAC2E,EAAE,EAAC,WAAW;gBAACP,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACrC5C,OAAA;kBAAQ2C,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbxD,OAAA;MAAS4B,GAAG,EAAEzB,cAAe;MAACwC,SAAS,EAAC,cAAc;MAAAC,QAAA,eACpD5C,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB5C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB5C,OAAA,CAACxB,MAAM,CAACkF,GAAG;YACTZ,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCX,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BnB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAErB5C,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5C,OAAA,CAACxB,MAAM,CAACkF,GAAG;gBACTZ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAG,CAAE;gBAC/BE,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAE,CAAE;gBAC9Bc,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1CpB,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,gBAE9G5C,OAAA,CAACjB,QAAQ;kBAAC4D,SAAS,EAAC;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uCAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbxD,OAAA;gBAAI2C,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,6BACE,EAAC,GAAG,eAC/B5C,OAAA;kBAAM2C,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC7B5C,OAAA,CAACvB,0BAA0B;oBAACkE,SAAS,EAAC;kBAAkC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAE7E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAELxD,OAAA;gBAAG2C,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,EAAC;cAIxE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNxD,OAAA,CAACxB,MAAM,CAACkF,GAAG;cACTZ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9Bc,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CpB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB5C,OAAA,CAACzB,IAAI;gBAAC2E,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClB5C,OAAA;kBAAQ2C,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAEpC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACPxD,OAAA,CAACzB,IAAI;gBAAC2E,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACf5C,OAAA;kBAAQ2C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGbxD,OAAA,CAACxB,MAAM,CAACkF,GAAG;cACTZ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9Bc,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CpB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE5B5C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA,CAACnB,OAAO;kBAAC8D,SAAS,EAAC;gBAA0B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDxD,OAAA;kBAAA4C,QAAA,EAAM;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNxD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA,CAAClB,MAAM;kBAAC6D,SAAS,EAAC;gBAAyB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CxD,OAAA;kBAAA4C,QAAA,EAAM;gBAAY;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNxD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA,CAACpB,QAAQ;kBAAC+D,SAAS,EAAC;gBAA0B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDxD,OAAA;kBAAA4C,QAAA,EAAM;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbxD,OAAA,CAACxB,MAAM,CAACkF,GAAG;YACTZ,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAG,CAAE;YAC/BX,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEtB5C,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5C,OAAA;gBACEgE,GAAG,EAAEtE,MAAO;gBACZuE,GAAG,EAAC,mBAAmB;gBACvBtB,SAAS,EAAC;cAAwC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eAGFxD,OAAA,CAACxB,MAAM,CAACkF,GAAG;gBACTT,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/Bc,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEI,MAAM,EAAEC;gBAAS,CAAE;gBAC9CxB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,eAEzE5C,OAAA,CAACrB,MAAM;kBAACgE,SAAS,EAAC;gBAA0B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAEbxD,OAAA,CAACxB,MAAM,CAACkF,GAAG;gBACTT,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9Bc,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEI,MAAM,EAAEC;gBAAS,CAAE;gBAC9CxB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAE7E5C,OAAA,CAACpB,QAAQ;kBAAC+D,SAAS,EAAC;gBAAyB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNxD,OAAA;cAAK2C,SAAS,EAAC;YAAgH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACtD,EAAA,CAtQID,IAAI;EAAA,QAMSX,WAAW,EAKQO,QAAQ;AAAA;AAAAuE,EAAA,GAXxCnE,IAAI;AAwQV,eAAeA,IAAI;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}