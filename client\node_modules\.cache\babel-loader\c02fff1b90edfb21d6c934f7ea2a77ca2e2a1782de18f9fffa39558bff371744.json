{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Login\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message } from \"antd\";\nimport React from \"react\";\nimport './index.css';\nimport Logo from '../../../assets/logo.png';\nimport { useDispatch } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { loginUser } from \"../../../apicalls/users\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n      const response = await loginUser(values);\n      dispatch(HideLoading());\n      console.log('Login response:', response); // Debug log\n\n      if (response.success) {\n        message.success(response.message);\n        localStorage.setItem(\"token\", response.data);\n\n        // Check if user is admin from the response\n        if (response.response && response.response.isAdmin) {\n          navigate(\"/admin/users\");\n        } else {\n          navigate(\"/user/quiz\");\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Login error:', error); // Debug log\n      message.error(error.message);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"brainwave-logo\",\n            className: \"h-16 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 mb-2\",\n            children: \"Welcome Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Sign in to your account to continue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: onFinish,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n              placeholder: \"Enter your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200\",\n                children: \"Don't have an account? Sign up\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"ZaVe+Vo7W9FMoQ/aTgBrV7UvA04=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["Form", "message", "React", "Logo", "useDispatch", "Link", "useNavigate", "loginUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "dispatch", "onFinish", "values", "response", "console", "log", "success", "localStorage", "setItem", "data", "isAdmin", "error", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "name", "label", "initialValue", "type", "placeholder", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Login/index.js"], "sourcesContent": ["import { Form, message } from \"antd\";\r\nimport React from \"react\";\r\nimport './index.css';\r\nimport Logo from '../../../assets/logo.png';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { loginUser } from \"../../../apicalls/users\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Login() {\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch();\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await loginUser(values);\r\n      dispatch(HideLoading());\r\n\r\n      console.log('Login response:', response); // Debug log\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        localStorage.setItem(\"token\", response.data);\r\n\r\n        // Check if user is admin from the response\r\n        if (response.response && response.response.isAdmin) {\r\n          navigate(\"/admin/users\");\r\n        } else {\r\n          navigate(\"/user/quiz\");\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Login error:', error); // Debug log\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4\">\r\n      <div className=\"w-full max-w-md\">\r\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\r\n          <div className=\"text-center mb-8\">\r\n            <img src={Logo} alt=\"brainwave-logo\" className=\"h-16 mx-auto mb-4\"/>\r\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Welcome Back</h1>\r\n            <p className=\"text-gray-600\">Sign in to your account to continue</p>\r\n          </div>\r\n\r\n          <Form layout=\"vertical\" onFinish={onFinish} className=\"space-y-6\">\r\n            <Form.Item name=\"email\" label=\"Email\" initialValue=\"\">\r\n              <input\r\n                type=\"email\"\r\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                placeholder=\"Enter your email\"\r\n              />\r\n            </Form.Item>\r\n\r\n            <Form.Item name=\"password\" label=\"Password\" initialValue=\"\">\r\n              <input\r\n                type=\"password\"\r\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                placeholder=\"Enter your password\"\r\n              />\r\n            </Form.Item>\r\n\r\n            <div className=\"space-y-4\">\r\n              <button\r\n                type=\"submit\"\r\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\"\r\n              >\r\n                Sign In\r\n              </button>\r\n\r\n              <div className=\"text-center\">\r\n                <Link\r\n                  to=\"/register\"\r\n                  className=\"text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200\"\r\n                >\r\n                  Don't have an account? Sign up\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </Form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AACpB,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACFF,QAAQ,CAACN,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMS,QAAQ,GAAG,MAAMX,SAAS,CAACU,MAAM,CAAC;MACxCF,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MAEvBW,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC,CAAC,CAAC;;MAE1C,IAAIA,QAAQ,CAACG,OAAO,EAAE;QACpBpB,OAAO,CAACoB,OAAO,CAACH,QAAQ,CAACjB,OAAO,CAAC;QACjCqB,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACM,IAAI,CAAC;;QAE5C;QACA,IAAIN,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,CAACO,OAAO,EAAE;UAClDX,QAAQ,CAAC,cAAc,CAAC;QAC1B,CAAC,MAAM;UACLA,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,MAAM;QACLb,OAAO,CAACyB,KAAK,CAACR,QAAQ,CAACjB,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdX,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MACvBW,OAAO,CAACO,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC,CAAC,CAAC;MACtCzB,OAAO,CAACyB,KAAK,CAACA,KAAK,CAACzB,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,oBACEU,OAAA;IAAKgB,SAAS,EAAC,8FAA8F;IAAAC,QAAA,eAC3GjB,OAAA;MAAKgB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BjB,OAAA;QAAKgB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxEjB,OAAA;UAAKgB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BjB,OAAA;YAAKkB,GAAG,EAAE1B,IAAK;YAAC2B,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACpEvB,OAAA;YAAIgB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEvB,OAAA;YAAGgB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAmC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAENvB,OAAA,CAACX,IAAI;UAACmC,MAAM,EAAC,UAAU;UAACnB,QAAQ,EAAEA,QAAS;UAACW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAC/DjB,OAAA,CAACX,IAAI,CAACoC,IAAI;YAACC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC,OAAO;YAACC,YAAY,EAAC,EAAE;YAAAX,QAAA,eACnDjB,OAAA;cACE6B,IAAI,EAAC,OAAO;cACZb,SAAS,EAAC,oKAAoK;cAC9Kc,WAAW,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZvB,OAAA,CAACX,IAAI,CAACoC,IAAI;YAACC,IAAI,EAAC,UAAU;YAACC,KAAK,EAAC,UAAU;YAACC,YAAY,EAAC,EAAE;YAAAX,QAAA,eACzDjB,OAAA;cACE6B,IAAI,EAAC,UAAU;cACfb,SAAS,EAAC,oKAAoK;cAC9Kc,WAAW,EAAC;YAAqB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZvB,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjB,OAAA;cACE6B,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,oKAAoK;cAAAC,QAAA,EAC/K;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETvB,OAAA;cAAKgB,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BjB,OAAA,CAACN,IAAI;gBACHqC,EAAE,EAAC,WAAW;gBACdf,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,EACzF;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrB,EAAA,CAhFQD,KAAK;EAAA,QACKN,WAAW,EACXF,WAAW;AAAA;AAAAuC,EAAA,GAFrB/B,KAAK;AAkFd,eAAeA,KAAK;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}