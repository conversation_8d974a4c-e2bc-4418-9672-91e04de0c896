{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Login\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message } from \"antd\";\nimport React from \"react\";\nimport './index.css';\nimport Logo from '../../../assets/logo.png';\nimport { useDispatch } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { loginUser } from \"../../../apicalls/users\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n      const response = await loginUser(values);\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(response.message);\n        localStorage.setItem(\"token\", response.data);\n\n        // Check if user is admin from the response\n        if (response.response && response.response.isAdmin) {\n          window.location.href = \"/admin/users\";\n        } else {\n          window.location.href = \"/user/quiz\";\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-center items-center h-screen w-screen bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card p-3 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"brainwave-logo\",\n            className: \"login-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          className: \"mt-2\",\n          onFinish: onFinish,\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"primary-contained-btn mt-2 w-100\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"underline\",\n              children: \"Not a member? Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"ZaVe+Vo7W9FMoQ/aTgBrV7UvA04=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["Form", "message", "React", "Logo", "useDispatch", "Link", "useNavigate", "loginUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "dispatch", "onFinish", "values", "response", "success", "localStorage", "setItem", "data", "isAdmin", "window", "location", "href", "error", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "name", "label", "initialValue", "type", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Login/index.js"], "sourcesContent": ["import { Form, message } from \"antd\";\r\nimport React from \"react\";\r\nimport './index.css';\r\nimport Logo from '../../../assets/logo.png';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { loginUser } from \"../../../apicalls/users\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Login() {\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch();\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await loginUser(values);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        localStorage.setItem(\"token\", response.data);\r\n\r\n        // Check if user is admin from the response\r\n        if (response.response && response.response.isAdmin) {\r\n          window.location.href = \"/admin/users\";\r\n        } else {\r\n          window.location.href = \"/user/quiz\";\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-center items-center h-screen w-screen bg-primary\">\r\n      <div className=\"card p-3 bg-white\">\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"flex justify-center\">\r\n            <img src={Logo} alt=\"brainwave-logo\" className=\"login-logo\"/>\r\n          </div>\r\n          <div className=\"divider\"></div>\r\n          <Form layout=\"vertical\" className=\"mt-2\" onFinish={onFinish}>\r\n            <Form.Item name=\"email\" label=\"Email\" initialValue=\"\">\r\n              <input type=\"text\" />\r\n            </Form.Item>\r\n            <Form.Item name=\"password\" label=\"Password\" initialValue=\"\">\r\n              <input type=\"password\" />\r\n            </Form.Item>\r\n\r\n            <div className=\"flex flex-col gap-2\">\r\n              <button\r\n                type=\"submit\"\r\n                className=\"primary-contained-btn mt-2 w-100\"\r\n              >\r\n                Login\r\n              </button>\r\n              <Link to=\"/register\" className=\"underline\">\r\n                Not a member? Register\r\n              </Link>\r\n            </div>\r\n          </Form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AACpB,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACFF,QAAQ,CAACN,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMS,QAAQ,GAAG,MAAMX,SAAS,CAACU,MAAM,CAAC;MACxCF,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIU,QAAQ,CAACC,OAAO,EAAE;QACpBlB,OAAO,CAACkB,OAAO,CAACD,QAAQ,CAACjB,OAAO,CAAC;QACjCmB,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAAC;;QAE5C;QACA,IAAIJ,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,CAACK,OAAO,EAAE;UAClDC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;QACvC,CAAC,MAAM;UACLF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;QACrC;MACF,CAAC,MAAM;QACLzB,OAAO,CAAC0B,KAAK,CAACT,QAAQ,CAACjB,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdZ,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MACvBP,OAAO,CAAC0B,KAAK,CAACA,KAAK,CAAC1B,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,oBACEU,OAAA;IAAKiB,SAAS,EAAC,+DAA+D;IAAAC,QAAA,eAC5ElB,OAAA;MAAKiB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChClB,OAAA;QAAKiB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BlB,OAAA;UAAKiB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClClB,OAAA;YAAKmB,GAAG,EAAE3B,IAAK;YAAC4B,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNxB,OAAA;UAAKiB,SAAS,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/BxB,OAAA,CAACX,IAAI;UAACoC,MAAM,EAAC,UAAU;UAACR,SAAS,EAAC,MAAM;UAACZ,QAAQ,EAAEA,QAAS;UAAAa,QAAA,gBAC1DlB,OAAA,CAACX,IAAI,CAACqC,IAAI;YAACC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC,OAAO;YAACC,YAAY,EAAC,EAAE;YAAAX,QAAA,eACnDlB,OAAA;cAAO8B,IAAI,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACZxB,OAAA,CAACX,IAAI,CAACqC,IAAI;YAACC,IAAI,EAAC,UAAU;YAACC,KAAK,EAAC,UAAU;YAACC,YAAY,EAAC,EAAE;YAAAX,QAAA,eACzDlB,OAAA;cAAO8B,IAAI,EAAC;YAAU;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEZxB,OAAA;YAAKiB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClClB,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC7C;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxB,OAAA,CAACN,IAAI;cAACqC,EAAE,EAAC,WAAW;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtB,EAAA,CA3DQD,KAAK;EAAA,QACKN,WAAW,EACXF,WAAW;AAAA;AAAAuC,EAAA,GAFrB/B,KAAK;AA6Dd,eAAeA,KAAK;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}