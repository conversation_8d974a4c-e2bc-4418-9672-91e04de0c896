import React from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbQuestionMark, Tb<PERSON><PERSON><PERSON>, TbTrophy, TbPlayerPlay } from 'react-icons/tb';
import { Card, Button } from './index';

const QuizCard = ({
  quiz,
  onStart,
  onView,
  showResults = false,
  userResult = null,
  className = '',
  ...props
}) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -8, scale: 1.02 }}
      transition={{ duration: 0.3 }}
      className={`quiz-card-modern ${className}`}
    >
      <Card
        interactive
        variant="default"
        className="quiz-card overflow-hidden h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
        {...props}
      >
        <div className="bg-gradient-to-r from-primary-500 to-blue-600 p-6 text-white">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <h3 className="text-xl font-bold mb-2 line-clamp-2">{quiz.name}</h3>
              <p className="text-blue-100 text-sm line-clamp-2 opacity-90">
                {quiz.description || 'Test your knowledge with this comprehensive quiz'}
              </p>
            </div>
            {quiz.difficulty && (
              <span className="px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white backdrop-blur-sm">
                {quiz.difficulty}
              </span>
            )}
          </div>
        </div>

        <div className="p-6 pb-4 bg-white">
          <div className="grid grid-cols-3 gap-3 mb-6">
            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <TbQuestionMark className="w-5 h-5 text-primary-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-gray-900">{quiz.questions?.length || 0}</div>
              <div className="text-xs text-gray-500">Questions</div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <TbClock className="w-5 h-5 text-primary-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-gray-900">{quiz.duration || 30}</div>
              <div className="text-xs text-gray-500">Minutes</div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <TbUsers className="w-5 h-5 text-primary-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-gray-900">{quiz.attempts || 0}</div>
              <div className="text-xs text-gray-500">Attempts</div>
            </div>
          </div>

          {quiz.subject && (
            <div className="mb-4">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-primary-100 to-blue-100 text-primary-800 border border-primary-200">
                📚 {quiz.subject}
              </span>
            </div>
          )}

          {showResults && userResult && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 mb-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TbTrophy className="w-5 h-5 text-yellow-500" />
                  <span className="text-sm font-semibold text-gray-700">Your Best Score</span>
                </div>
                <div className={`text-xl font-bold ${getScoreColor(userResult.percentage)}`}>
                  {userResult.percentage}%
                </div>
              </div>
              <div className="mt-2 text-xs text-gray-600 flex items-center space-x-2">
                <span>{userResult.correctAnswers}/{userResult.totalQuestions} correct</span>
                <span>•</span>
                <span>Completed {new Date(userResult.completedAt).toLocaleDateString()}</span>
              </div>
            </motion.div>
          )}
        </div>

        <div className="px-6 pb-6 bg-gray-50 border-t border-gray-100">
          <div className="flex space-x-3 pt-4">
            <Button
              variant="primary"
              size="md"
              className="flex-1 bg-gradient-to-r from-primary-600 to-blue-600 hover:from-primary-700 hover:to-blue-700 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              onClick={onStart}
              icon={<TbPlayerPlay />}
            >
              {showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}
            </Button>

            {showResults && onView && (
              <Button
                variant="secondary"
                size="md"
                className="bg-white border-2 border-primary-200 text-primary-600 hover:bg-primary-50 hover:border-primary-300 transform hover:scale-105 transition-all duration-200"
                onClick={onView}
                icon={<TbTrophy />}
              >
                View Results
              </Button>
            )}
          </div>
        </div>

        {quiz.progress && quiz.progress > 0 && quiz.progress < 100 && (
          <div className="px-6 pb-4">
            <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
              <span>Progress</span>
              <span>{quiz.progress}%</span>
            </div>
            <div className="progress-bar">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${quiz.progress}%` }}
                transition={{ duration: 0.5 }}
                className="progress-fill"
              />
            </div>
          </div>
        )}

        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-primary-600/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
          whileHover={{ opacity: 1 }}
        />
      </Card>
    </motion.div>
  );
};

export const QuizGrid = ({ quizzes, onQuizStart, onQuizView, showResults = false, userResults = {}, className = '' }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {quizzes.map((quiz, index) => (
        <motion.div
          key={quiz._id || index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <QuizCard
            quiz={quiz}
            onStart={() => onQuizStart(quiz)}
            onView={onQuizView ? () => onQuizView(quiz) : undefined}
            showResults={showResults}
            userResult={userResults[quiz._id]}
          />
        </motion.div>
      ))}
    </div>
  );
};

export default QuizCard;
