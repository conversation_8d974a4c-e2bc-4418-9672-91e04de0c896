{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\Loading.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Loading = ({\n  size = 'md',\n  variant = 'spinner',\n  text = '',\n  fullScreen = false,\n  className = ''\n}) => {\n  const sizes = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  };\n  const Spinner = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${sizes[size]} ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animate-spin rounded-full border-2 border-gray-200 border-t-primary-600 h-full w-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n  const Dots = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex space-x-1\",\n    children: [0, 1, 2].map(i => /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"w-2 h-2 bg-primary-600 rounded-full\",\n      animate: {\n        scale: [1, 1.2, 1],\n        opacity: [0.7, 1, 0.7]\n      },\n      transition: {\n        duration: 0.6,\n        repeat: Infinity,\n        delay: i * 0.2\n      }\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n  const Pulse = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    className: `bg-primary-600 rounded-full ${sizes[size]}`,\n    animate: {\n      scale: [1, 1.2, 1],\n      opacity: [0.7, 1, 0.7]\n    },\n    transition: {\n      duration: 1,\n      repeat: Infinity\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n  const Skeleton = ({\n    className = ''\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `animate-pulse bg-gray-200 rounded ${className}`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n  const renderLoader = () => {\n    switch (variant) {\n      case 'dots':\n        return /*#__PURE__*/_jsxDEV(Dots, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 16\n        }, this);\n      case 'pulse':\n        return /*#__PURE__*/_jsxDEV(Pulse, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 16\n        }, this);\n      case 'skeleton':\n        return /*#__PURE__*/_jsxDEV(Skeleton, {\n          className: className\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Spinner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const content = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center space-y-4\",\n    children: [renderLoader(), text && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 text-sm font-medium animate-pulse\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n  if (fullScreen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center\",\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this);\n  }\n  return content;\n};\n\n// Skeleton components for different layouts\n_c = Loading;\nLoading.Skeleton = {\n  Text: ({\n    lines = 3,\n    className = ''\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-2 ${className}`,\n    children: Array.from({\n      length: lines\n    }, (_, i) => /*#__PURE__*/_jsxDEV(Skeleton, {\n      className: `h-4 ${i === lines - 1 ? 'w-3/4' : 'w-full'}`\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this),\n  Card: ({\n    className = ''\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `p-6 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n      className: \"h-6 w-1/3 mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n      className: \"h-4 w-full mb-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n      className: \"h-4 w-full mb-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n      className: \"h-4 w-2/3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this),\n  Avatar: ({\n    size = 'md',\n    className = ''\n  }) => {\n    const avatarSizes = {\n      sm: 'w-8 h-8',\n      md: 'w-12 h-12',\n      lg: 'w-16 h-16',\n      xl: 'w-20 h-20'\n    };\n    return /*#__PURE__*/_jsxDEV(Skeleton, {\n      className: `${avatarSizes[size]} rounded-full ${className}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 12\n    }, this);\n  },\n  Button: ({\n    className = ''\n  }) => /*#__PURE__*/_jsxDEV(Skeleton, {\n    className: `h-10 w-24 rounded-lg ${className}`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this)\n};\nexport default Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");", "map": {"version": 3, "names": ["React", "motion", "jsxDEV", "_jsxDEV", "Loading", "size", "variant", "text", "fullScreen", "className", "sizes", "sm", "md", "lg", "xl", "Spinner", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Dots", "map", "i", "div", "animate", "scale", "opacity", "transition", "duration", "repeat", "Infinity", "delay", "Pulse", "Skeleton", "renderLoader", "content", "_c", "Text", "lines", "Array", "from", "length", "_", "Card", "Avatar", "avatar<PERSON><PERSON>", "<PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/Loading.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst Loading = ({ \n  size = 'md', \n  variant = 'spinner', \n  text = '', \n  fullScreen = false,\n  className = '' \n}) => {\n  const sizes = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16',\n  };\n\n  const Spinner = () => (\n    <div className={`${sizes[size]} ${className}`}>\n      <div className=\"animate-spin rounded-full border-2 border-gray-200 border-t-primary-600 h-full w-full\"></div>\n    </div>\n  );\n\n  const Dots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((i) => (\n        <motion.div\n          key={i}\n          className=\"w-2 h-2 bg-primary-600 rounded-full\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 0.6,\n            repeat: Infinity,\n            delay: i * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const Pulse = () => (\n    <motion.div\n      className={`bg-primary-600 rounded-full ${sizes[size]}`}\n      animate={{\n        scale: [1, 1.2, 1],\n        opacity: [0.7, 1, 0.7],\n      }}\n      transition={{\n        duration: 1,\n        repeat: Infinity,\n      }}\n    />\n  );\n\n  const Skeleton = ({ className = '' }) => (\n    <div className={`animate-pulse bg-gray-200 rounded ${className}`} />\n  );\n\n  const renderLoader = () => {\n    switch (variant) {\n      case 'dots':\n        return <Dots />;\n      case 'pulse':\n        return <Pulse />;\n      case 'skeleton':\n        return <Skeleton className={className} />;\n      default:\n        return <Spinner />;\n    }\n  };\n\n  const content = (\n    <div className=\"flex flex-col items-center justify-center space-y-4\">\n      {renderLoader()}\n      {text && (\n        <p className=\"text-gray-600 text-sm font-medium animate-pulse\">\n          {text}\n        </p>\n      )}\n    </div>\n  );\n\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center\">\n        {content}\n      </div>\n    );\n  }\n\n  return content;\n};\n\n// Skeleton components for different layouts\nLoading.Skeleton = {\n  Text: ({ lines = 3, className = '' }) => (\n    <div className={`space-y-2 ${className}`}>\n      {Array.from({ length: lines }, (_, i) => (\n        <Skeleton\n          key={i}\n          className={`h-4 ${i === lines - 1 ? 'w-3/4' : 'w-full'}`}\n        />\n      ))}\n    </div>\n  ),\n  \n  Card: ({ className = '' }) => (\n    <div className={`p-6 ${className}`}>\n      <Skeleton className=\"h-6 w-1/3 mb-4\" />\n      <Skeleton className=\"h-4 w-full mb-2\" />\n      <Skeleton className=\"h-4 w-full mb-2\" />\n      <Skeleton className=\"h-4 w-2/3\" />\n    </div>\n  ),\n  \n  Avatar: ({ size = 'md', className = '' }) => {\n    const avatarSizes = {\n      sm: 'w-8 h-8',\n      md: 'w-12 h-12',\n      lg: 'w-16 h-16',\n      xl: 'w-20 h-20',\n    };\n    return <Skeleton className={`${avatarSizes[size]} rounded-full ${className}`} />;\n  },\n  \n  Button: ({ className = '' }) => (\n    <Skeleton className={`h-10 w-24 rounded-lg ${className}`} />\n  ),\n};\n\nexport default Loading;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,OAAO,GAAGA,CAAC;EACfC,IAAI,GAAG,IAAI;EACXC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,EAAE;EACTC,UAAU,GAAG,KAAK;EAClBC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAG;IACZC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,OAAO,GAAGA,CAAA,kBACdZ,OAAA;IAAKM,SAAS,EAAG,GAAEC,KAAK,CAACL,IAAI,CAAE,IAAGI,SAAU,EAAE;IAAAO,QAAA,eAC5Cb,OAAA;MAAKM,SAAS,EAAC;IAAuF;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1G,CACN;EAED,MAAMC,IAAI,GAAGA,CAAA,kBACXlB,OAAA;IAAKM,SAAS,EAAC,gBAAgB;IAAAO,QAAA,EAC5B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACM,GAAG,CAAEC,CAAC,iBACfpB,OAAA,CAACF,MAAM,CAACuB,GAAG;MAETf,SAAS,EAAC,qCAAqC;MAC/CgB,OAAO,EAAE;QACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;MACvB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAEC,QAAQ;QAChBC,KAAK,EAAET,CAAC,GAAG;MACb;IAAE,GAVGA,CAAC;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAWP,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMa,KAAK,GAAGA,CAAA,kBACZ9B,OAAA,CAACF,MAAM,CAACuB,GAAG;IACTf,SAAS,EAAG,+BAA8BC,KAAK,CAACL,IAAI,CAAE,EAAE;IACxDoB,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAClBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;IACvB,CAAE;IACFC,UAAU,EAAE;MACVC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAEC;IACV;EAAE;IAAAd,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAED,MAAMc,QAAQ,GAAGA,CAAC;IAAEzB,SAAS,GAAG;EAAG,CAAC,kBAClCN,OAAA;IAAKM,SAAS,EAAG,qCAAoCA,SAAU;EAAE;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CACpE;EAED,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQ7B,OAAO;MACb,KAAK,MAAM;QACT,oBAAOH,OAAA,CAACkB,IAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjB,KAAK,OAAO;QACV,oBAAOjB,OAAA,CAAC8B,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClB,KAAK,UAAU;QACb,oBAAOjB,OAAA,CAAC+B,QAAQ;UAACzB,SAAS,EAAEA;QAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3C;QACE,oBAAOjB,OAAA,CAACY,OAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtB;EACF,CAAC;EAED,MAAMgB,OAAO,gBACXjC,OAAA;IAAKM,SAAS,EAAC,qDAAqD;IAAAO,QAAA,GACjEmB,YAAY,CAAC,CAAC,EACd5B,IAAI,iBACHJ,OAAA;MAAGM,SAAS,EAAC,iDAAiD;MAAAO,QAAA,EAC3DT;IAAI;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,IAAIZ,UAAU,EAAE;IACd,oBACEL,OAAA;MAAKM,SAAS,EAAC,kFAAkF;MAAAO,QAAA,EAC9FoB;IAAO;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,OAAOgB,OAAO;AAChB,CAAC;;AAED;AAAAC,EAAA,GA7FMjC,OAAO;AA8FbA,OAAO,CAAC8B,QAAQ,GAAG;EACjBI,IAAI,EAAEA,CAAC;IAAEC,KAAK,GAAG,CAAC;IAAE9B,SAAS,GAAG;EAAG,CAAC,kBAClCN,OAAA;IAAKM,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAAO,QAAA,EACtCwB,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEH;IAAM,CAAC,EAAE,CAACI,CAAC,EAAEpB,CAAC,kBAClCpB,OAAA,CAAC+B,QAAQ;MAEPzB,SAAS,EAAG,OAAMc,CAAC,KAAKgB,KAAK,GAAG,CAAC,GAAG,OAAO,GAAG,QAAS;IAAE,GADpDhB,CAAC;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAEDwB,IAAI,EAAEA,CAAC;IAAEnC,SAAS,GAAG;EAAG,CAAC,kBACvBN,OAAA;IAAKM,SAAS,EAAG,OAAMA,SAAU,EAAE;IAAAO,QAAA,gBACjCb,OAAA,CAAC+B,QAAQ;MAACzB,SAAS,EAAC;IAAgB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvCjB,OAAA,CAAC+B,QAAQ;MAACzB,SAAS,EAAC;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxCjB,OAAA,CAAC+B,QAAQ;MAACzB,SAAS,EAAC;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxCjB,OAAA,CAAC+B,QAAQ;MAACzB,SAAS,EAAC;IAAW;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/B,CACN;EAEDyB,MAAM,EAAEA,CAAC;IAAExC,IAAI,GAAG,IAAI;IAAEI,SAAS,GAAG;EAAG,CAAC,KAAK;IAC3C,MAAMqC,WAAW,GAAG;MAClBnC,EAAE,EAAE,SAAS;MACbC,EAAE,EAAE,WAAW;MACfC,EAAE,EAAE,WAAW;MACfC,EAAE,EAAE;IACN,CAAC;IACD,oBAAOX,OAAA,CAAC+B,QAAQ;MAACzB,SAAS,EAAG,GAAEqC,WAAW,CAACzC,IAAI,CAAE,iBAAgBI,SAAU;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClF,CAAC;EAED2B,MAAM,EAAEA,CAAC;IAAEtC,SAAS,GAAG;EAAG,CAAC,kBACzBN,OAAA,CAAC+B,QAAQ;IAACzB,SAAS,EAAG,wBAAuBA,SAAU;EAAE;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAE/D,CAAC;AAED,eAAehB,OAAO;AAAC,IAAAiC,EAAA;AAAAW,YAAA,CAAAX,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}