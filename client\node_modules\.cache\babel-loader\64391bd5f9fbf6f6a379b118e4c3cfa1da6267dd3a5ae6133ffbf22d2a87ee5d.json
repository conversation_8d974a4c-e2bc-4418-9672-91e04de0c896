{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbQuestionMark, TbChartBar, TbSchool, TbMenu2, TbX, TbMoon, TbSun } from \"react-icons/tb\";\nimport { AiOutlinePlus } from \"react-icons/ai\";\nimport { message, Rate } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReviews } from \"../../../apicalls/reviews\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport Image2 from \"../../../assets/collage-2.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport { useTheme } from \"../../../contexts/ThemeContext\";\nimport { Button, Card } from \"../../../components/modern\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const aboutUsSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [reviews, setReviews] = useState([]); // Initialize as an array\n  const dispatch = useDispatch();\n  const [menuOpen, setMenuOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const getReviews = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getAllReviews();\n      if (response.success) {\n        setReviews(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    getReviews();\n  }, []);\n  const scrollToSection = (ref, offset = 30) => {\n    if (ref && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      // Assume contactUs returns the parsed JSON response\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        }); // Reset form\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\",\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern fixed w-full top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-modern\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8 text-primary-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n              children: [\"Brain\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-primary-600\",\n                children: \"Wave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 22\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(homeSectionRef),\n              className: \"nav-item\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(aboutUsSectionRef),\n              className: \"nav-item\",\n              children: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(reviewsSectionRef),\n              className: \"nav-item\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactUsRef),\n              className: \"nav-item\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleTheme,\n              className: \"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n              children: isDarkMode ? /*#__PURE__*/_jsxDEV(TbSun, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 31\n              }, this) : /*#__PURE__*/_jsxDEV(TbMoon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 63\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"ghost\",\n                  size: \"sm\",\n                  children: \"Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"primary\",\n                  size: \"sm\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setMenuOpen(!menuOpen),\n              className: \"md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n              children: menuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 29\n              }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 59\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), menuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          className: \"md:hidden py-4 border-t border-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(homeSectionRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(aboutUsSectionRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(reviewsSectionRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(contactUsRef);\n                setMenuOpen(false);\n              },\n              className: \"nav-item text-left\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"flex-1\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"ghost\",\n                  size: \"sm\",\n                  className: \"w-full\",\n                  children: \"Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"flex-1\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"primary\",\n                  size: \"sm\",\n                  className: \"w-full\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: homeSectionRef,\n      className: \"pt-20 pb-16 lg:pt-24 lg:pb-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-modern\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  duration: 0.6,\n                  delay: 0.2\n                },\n                className: \"inline-flex items-center px-4 py-2 bg-primary-50 text-primary-700 rounded-full text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this), \"#1 Educational Platform in Tanzania\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"heading-1\",\n                children: [\"Fueling Bright Futures with\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gradient\",\n                  children: [/*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                    className: \"inline w-12 h-12 lg:w-16 lg:h-16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 21\n                  }, this), \"Education.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-gray-600 dark:text-gray-300 leading-relaxed\",\n                children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"gradient\",\n                  size: \"lg\",\n                  className: \"w-full sm:w-auto\",\n                  icon: /*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 27\n                  }, this),\n                  iconPosition: \"right\",\n                  children: \"Get Started Free\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"secondary\",\n                  size: \"lg\",\n                  className: \"w-full sm:w-auto\",\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"flex items-center space-x-6 text-sm text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  className: \"w-5 h-5 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-5 h-5 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: Image1,\n                alt: \"Students Learning\",\n                className: \"w-full h-auto rounded-2xl shadow-large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                className: \"absolute -top-4 -left-4 bg-white rounded-xl shadow-medium p-4\",\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  className: \"w-8 h-8 text-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                className: \"absolute -bottom-4 -right-4 bg-white rounded-xl shadow-medium p-4\",\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-8 h-8 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-br from-primary-100 to-blue-100 rounded-2xl transform rotate-3 scale-105 -z-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white dark:bg-gray-800\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-modern\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: [{\n            number: \"15K+\",\n            text: \"Students Joined\",\n            icon: TbUsers,\n            color: \"text-blue-600\"\n          }, {\n            number: \"300+\",\n            text: \"Expert Mentors\",\n            icon: TbSchool,\n            color: \"text-green-600\"\n          }, {\n            number: \"7K+\",\n            text: \"Success Stories\",\n            icon: TbTrophy,\n            color: \"text-yellow-600\"\n          }, {\n            number: \"250+\",\n            text: \"Trendy Courses\",\n            icon: TbBook,\n            color: \"text-purple-600\"\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-center group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-primary-100 to-blue-100 mb-4 group-hover:scale-110 transition-transform duration-300`,\n                children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                  className: `w-8 h-8 ${stat.color}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  scale: 0\n                },\n                whileInView: {\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: index * 0.1 + 0.3\n                },\n                viewport: {\n                  once: true\n                },\n                className: \"absolute -top-2 -right-2 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-xs font-bold\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              whileInView: {\n                opacity: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1 + 0.4\n              },\n              viewport: {\n                once: true\n              },\n              className: \"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-300 font-medium\",\n              children: stat.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: aboutUsSectionRef,\n      className: \"section-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title\",\n          children: \"Discover knowledge in limitless realms.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"para\",\n          children: \"Education serves as the cornerstone of personal and societal development. It is a dynamic process that empowers individuals with the knowledge, skills, and critical thinking abilities essential for success.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"btn-container\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/user/about-us\",\n            className: \"btn btn-1\",\n            children: \"Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-2\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: Image2,\n          alt: \"Collage-1\",\n          className: \"collage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"section-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title\",\n          children: [\"Reviews from \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 26\n          }, this), \"some students\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-2\",\n        children: reviews.length !== 0 ? reviews.map((review, index) => {\n          var _review$user;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"review-card\",\n            children: [/*#__PURE__*/_jsxDEV(Rate, {\n              defaultValue: review.rating,\n              className: \"rate\",\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text\",\n              children: [\"\\\"\", review.text, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"seperator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"name\",\n              children: (_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this);\n        }) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"No reviews yet.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"contact-section section-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title\",\n          children: \"Contact Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-container\",\n        style: {\n          marginTop: \"40px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-box\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"contact-form\",\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"contact-label\",\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                placeholder: \"Your Name\",\n                className: \"contact-input\",\n                value: formData.name,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"contact-label\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                placeholder: \"Your Email\",\n                className: \"contact-input\",\n                value: formData.email,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"contact-label\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                placeholder: \"Your Message\",\n                className: \"contact-textarea\",\n                style: {\n                  width: \"93.5%\",\n                  padding: \"10px\"\n                },\n                value: formData.message,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"contact-submit\",\n              disabled: loading,\n              children: loading ? \"Sending...\" : \"Send Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"response-message\",\n              children: responseMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"ZfrqPLoKU84xoz2TSTTYTV3LbS0=\", false, function () {\n  return [useDispatch, useTheme];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbQuestionMark", "TbChartBar", "TbSchool", "TbMenu2", "TbX", "TbMoon", "TbSun", "AiOutlinePlus", "message", "Rate", "useDispatch", "HideLoading", "ShowLoading", "getAllReviews", "Image1", "Image2", "contactUs", "useTheme", "<PERSON><PERSON>", "Card", "jsxDEV", "_jsxDEV", "Home", "_s", "homeSectionRef", "aboutUsSectionRef", "reviewsSectionRef", "contactUsRef", "reviews", "setReviews", "dispatch", "menuOpen", "setMenuOpen", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "getReviews", "response", "success", "data", "error", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "isDarkMode", "toggleTheme", "className", "children", "nav", "initial", "y", "opacity", "animate", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "size", "div", "exit", "x", "transition", "duration", "delay", "icon", "iconPosition", "src", "alt", "repeat", "Infinity", "whileInView", "viewport", "once", "number", "text", "color", "map", "stat", "index", "scale", "length", "review", "_review$user", "defaultValue", "rating", "disabled", "user", "style", "marginTop", "onSubmit", "type", "placeholder", "onChange", "required", "width", "padding", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { <PERSON> } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbQuestionMark,\r\n  TbChartBar,\r\n  TbSchool,\r\n  TbMenu2,\r\n  TbX,\r\n  TbMoon,\r\n  TbSun\r\n} from \"react-icons/tb\";\r\nimport { AiOutlinePlus } from \"react-icons/ai\";\r\nimport { message, Rate } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReviews } from \"../../../apicalls/reviews\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport Image2 from \"../../../assets/collage-2.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport { useTheme } from \"../../../contexts/ThemeContext\";\r\nimport { <PERSON><PERSON>, <PERSON> } from \"../../../components/modern\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const aboutUsSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [reviews, setReviews] = useState([]); // Initialize as an array\r\n  const dispatch = useDispatch();\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    message: \"\",\r\n  });\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n\r\n  const getReviews = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getAllReviews();\r\n      if (response.success) {\r\n        setReviews(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  useEffect(() => {\r\n    getReviews();\r\n  }, []);\r\n\r\n  const scrollToSection = (ref, offset = 30) => {\r\n    if (ref && ref.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({\r\n        top: sectionTop - offset,\r\n        behavior: \"smooth\"\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n\r\n    try {\r\n      // Assume contactUs returns the parsed JSON response\r\n      const data = await contactUs(formData);\r\n\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" }); // Reset form\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  return (\r\n    <div className=\"Home min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\">\r\n      {/* Modern Navigation */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-modern fixed w-full top-0 z-50\"\r\n      >\r\n        <div className=\"container-modern\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Logo */}\r\n            <Link to=\"/\" className=\"flex items-center space-x-2\">\r\n              <TbBrain className=\"w-8 h-8 text-primary-600\" />\r\n              <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n                Brain<span className=\"text-primary-600\">Wave</span>\r\n              </span>\r\n            </Link>\r\n\r\n            {/* Desktop Navigation */}\r\n            <div className=\"hidden md:flex items-center space-x-8\">\r\n              <button\r\n                onClick={() => scrollToSection(homeSectionRef)}\r\n                className=\"nav-item\"\r\n              >\r\n                Home\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(aboutUsSectionRef)}\r\n                className=\"nav-item\"\r\n              >\r\n                About Us\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(reviewsSectionRef)}\r\n                className=\"nav-item\"\r\n              >\r\n                Reviews\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(contactUsRef)}\r\n                className=\"nav-item\"\r\n              >\r\n                Contact Us\r\n              </button>\r\n            </div>\r\n\r\n            {/* Right Section */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Theme Toggle */}\r\n              <button\r\n                onClick={toggleTheme}\r\n                className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {isDarkMode ? <TbSun className=\"w-5 h-5\" /> : <TbMoon className=\"w-5 h-5\" />}\r\n              </button>\r\n\r\n              {/* Auth Buttons - Desktop */}\r\n              <div className=\"hidden md:flex items-center space-x-3\">\r\n                <Link to=\"/login\">\r\n                  <Button variant=\"ghost\" size=\"sm\">Login</Button>\r\n                </Link>\r\n                <Link to=\"/register\">\r\n                  <Button variant=\"primary\" size=\"sm\">Sign Up</Button>\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                onClick={() => setMenuOpen(!menuOpen)}\r\n                className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {menuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          {menuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              className=\"md:hidden py-4 border-t border-gray-100\"\r\n            >\r\n              <div className=\"flex flex-col space-y-2\">\r\n                <button\r\n                  onClick={() => {\r\n                    scrollToSection(homeSectionRef);\r\n                    setMenuOpen(false);\r\n                  }}\r\n                  className=\"nav-item text-left\"\r\n                >\r\n                  Home\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    scrollToSection(aboutUsSectionRef);\r\n                    setMenuOpen(false);\r\n                  }}\r\n                  className=\"nav-item text-left\"\r\n                >\r\n                  About Us\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    scrollToSection(reviewsSectionRef);\r\n                    setMenuOpen(false);\r\n                  }}\r\n                  className=\"nav-item text-left\"\r\n                >\r\n                  Reviews\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    scrollToSection(contactUsRef);\r\n                    setMenuOpen(false);\r\n                  }}\r\n                  className=\"nav-item text-left\"\r\n                >\r\n                  Contact Us\r\n                </button>\r\n                <div className=\"flex space-x-3 pt-4\">\r\n                  <Link to=\"/login\" className=\"flex-1\">\r\n                    <Button variant=\"ghost\" size=\"sm\" className=\"w-full\">Login</Button>\r\n                  </Link>\r\n                  <Link to=\"/register\" className=\"flex-1\">\r\n                    <Button variant=\"primary\" size=\"sm\" className=\"w-full\">Sign Up</Button>\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n      {/* Modern Hero Section */}\r\n      <section ref={homeSectionRef} className=\"pt-20 pb-16 lg:pt-24 lg:pb-20\">\r\n        <div className=\"container-modern\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"space-y-8\"\r\n            >\r\n              <div className=\"space-y-4\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"inline-flex items-center px-4 py-2 bg-primary-50 text-primary-700 rounded-full text-sm font-medium\"\r\n                >\r\n                  <TbSchool className=\"w-4 h-4 mr-2\" />\r\n                  #1 Educational Platform in Tanzania\r\n                </motion.div>\r\n\r\n                <h1 className=\"heading-1\">\r\n                  Fueling Bright Futures with{\" \"}\r\n                  <span className=\"text-gradient\">\r\n                    <TbArrowBigRightLinesFilled className=\"inline w-12 h-12 lg:w-16 lg:h-16\" />\r\n                    Education.\r\n                  </span>\r\n                </h1>\r\n\r\n                <p className=\"text-xl text-gray-600 dark:text-gray-300 leading-relaxed\">\r\n                  Discover limitless learning opportunities with our comprehensive\r\n                  online study platform. Study anywhere, anytime, and achieve your\r\n                  academic goals with confidence.\r\n                </p>\r\n              </div>\r\n\r\n              {/* CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"flex flex-col sm:flex-row gap-4\"\r\n              >\r\n                <Link to=\"/register\">\r\n                  <Button\r\n                    variant=\"gradient\"\r\n                    size=\"lg\"\r\n                    className=\"w-full sm:w-auto\"\r\n                    icon={<TbArrowBigRightLinesFilled />}\r\n                    iconPosition=\"right\"\r\n                  >\r\n                    Get Started Free\r\n                  </Button>\r\n                </Link>\r\n                <Link to=\"/login\">\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    size=\"lg\"\r\n                    className=\"w-full sm:w-auto\"\r\n                  >\r\n                    Sign In\r\n                  </Button>\r\n                </Link>\r\n              </motion.div>\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"flex items-center space-x-6 text-sm text-gray-500\"\r\n              >\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <TbUsers className=\"w-5 h-5 text-primary-600\" />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <TbStar className=\"w-5 h-5 text-yellow-500\" />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <TbTrophy className=\"w-5 h-5 text-primary-600\" />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"relative\"\r\n            >\r\n              <div className=\"relative z-10\">\r\n                <img\r\n                  src={Image1}\r\n                  alt=\"Students Learning\"\r\n                  className=\"w-full h-auto rounded-2xl shadow-large\"\r\n                />\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"absolute -top-4 -left-4 bg-white rounded-xl shadow-medium p-4\"\r\n                >\r\n                  <TbBook className=\"w-8 h-8 text-primary-600\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"absolute -bottom-4 -right-4 bg-white rounded-xl shadow-medium p-4\"\r\n                >\r\n                  <TbTrophy className=\"w-8 h-8 text-yellow-500\" />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Background Decoration */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-primary-100 to-blue-100 rounded-2xl transform rotate-3 scale-105 -z-10\"></div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      {/* Modern Statistics Section */}\r\n      <section className=\"py-16 bg-white dark:bg-gray-800\">\r\n        <div className=\"container-modern\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Students Joined\", icon: TbUsers, color: \"text-blue-600\" },\r\n              { number: \"300+\", text: \"Expert Mentors\", icon: TbSchool, color: \"text-green-600\" },\r\n              { number: \"7K+\", text: \"Success Stories\", icon: TbTrophy, color: \"text-yellow-600\" },\r\n              { number: \"250+\", text: \"Trendy Courses\", icon: TbBook, color: \"text-purple-600\" },\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-center group\"\r\n              >\r\n                <div className=\"relative\">\r\n                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-primary-100 to-blue-100 mb-4 group-hover:scale-110 transition-transform duration-300`}>\r\n                    <stat.icon className={`w-8 h-8 ${stat.color}`} />\r\n                  </div>\r\n                  <motion.div\r\n                    initial={{ scale: 0 }}\r\n                    whileInView={{ scale: 1 }}\r\n                    transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}\r\n                    viewport={{ once: true }}\r\n                    className=\"absolute -top-2 -right-2 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center\"\r\n                  >\r\n                    <span className=\"text-white text-xs font-bold\">✓</span>\r\n                  </motion.div>\r\n                </div>\r\n                <motion.div\r\n                  initial={{ opacity: 0 }}\r\n                  whileInView={{ opacity: 1 }}\r\n                  transition={{ duration: 0.6, delay: index * 0.1 + 0.4 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2\"\r\n                >\r\n                  {stat.number}\r\n                </motion.div>\r\n                <p className=\"text-gray-600 dark:text-gray-300 font-medium\">\r\n                  {stat.text}\r\n                </p>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n      <section ref={aboutUsSectionRef} className=\"section-3\">\r\n        <div className=\"content-1\">\r\n          <div className=\"title\">Discover knowledge in limitless realms.</div>\r\n          <p className=\"para\">\r\n            Education serves as the cornerstone of personal and societal\r\n            development. It is a dynamic process that empowers individuals with\r\n            the knowledge, skills, and critical thinking abilities essential for\r\n            success.\r\n          </p>\r\n          <div className=\"btn-container\">\r\n            <Link to=\"/user/about-us\" className=\"btn btn-1\">\r\n              Learn More\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <div className=\"content-2\">\r\n          <img src={Image2} alt=\"Collage-1\" className=\"collage\" />\r\n        </div>\r\n      </section>\r\n      <section ref={reviewsSectionRef} className=\"section-4\">\r\n        <div className=\"content-1\">\r\n          <div className=\"title\">\r\n            Reviews from <br />\r\n            some students\r\n          </div>\r\n        </div>\r\n        <div className=\"content-2\">\r\n          {reviews.length !== 0 ? (\r\n            reviews.map((review, index) => (\r\n              <div key={index} className=\"review-card\">\r\n                <Rate defaultValue={review.rating} className=\"rate\" disabled />\r\n                <div className=\"text\">\"{review.text}\"</div>\r\n                <div className=\"seperator\"></div>\r\n                <div className=\"name\">{review.user?.name}</div>\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div>No reviews yet.</div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      <section ref={contactUsRef} className=\"contact-section section-4\">\r\n        <div className=\"content-1\">\r\n          <div className=\"title\">Contact Us</div>\r\n        </div>\r\n        <div className=\"contact-container\" style={{ marginTop: \"40px\" }}>\r\n          <div className=\"contact-box\">\r\n            <form className=\"contact-form\" onSubmit={handleSubmit}>\r\n              <div className=\"contact-field\">\r\n                <label className=\"contact-label\">Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"name\"\r\n                  placeholder=\"Your Name\"\r\n                  className=\"contact-input\"\r\n                  value={formData.name}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"contact-field\">\r\n                <label className=\"contact-label\">Email</label>\r\n                <input\r\n                  type=\"email\"\r\n                  name=\"email\"\r\n                  placeholder=\"Your Email\"\r\n                  className=\"contact-input\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"contact-field\">\r\n                <label className=\"contact-label\">Message</label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  placeholder=\"Your Message\"\r\n                  className=\"contact-textarea\"\r\n                  style={{ width: \"93.5%\", padding: \"10px\" }}\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  required\r\n                ></textarea>\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"contact-submit\"\r\n                disabled={loading}\r\n              >\r\n                {loading ? \"Sending...\" : \"Send Message\"}\r\n              </button>\r\n              {responseMessage && (\r\n                <p className=\"response-message\">{responseMessage}</p>\r\n              )}\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,cAAc,EACdC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,KAAK,QACA,gBAAgB;AACvB,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,OAAO,EAAEC,IAAI,QAAQ,MAAM;AACpC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,MAAM,EAAEC,IAAI,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMkC,iBAAiB,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMmC,iBAAiB,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMoC,YAAY,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C,MAAMyC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC;IACvC8C,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACT5B,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAMoD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BX,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAM7B,aAAa,CAAC,CAAC;MACtC,IAAI6B,QAAQ,CAACC,OAAO,EAAE;QACpBd,UAAU,CAACa,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLpC,OAAO,CAACqC,KAAK,CAACH,QAAQ,CAAClC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAACA,KAAK,CAACrC,OAAO,CAAC;IAC9B;IACAsB,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACdmD,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,IAAIA,GAAG,CAACE,OAAO,EAAE;MACtB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QACdC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QACxBO,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEtB,IAAI;MAAEuB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCzB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGuB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBvB,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IAEtB,IAAI;MACF;MACA,MAAMI,IAAI,GAAG,MAAM5B,SAAS,CAACiB,QAAQ,CAAC;MAEtC,IAAIW,IAAI,CAACD,OAAO,EAAE;QAChBnC,OAAO,CAACmC,OAAO,CAAC,4BAA4B,CAAC;QAC7CH,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAE5B,OAAO,EAAE;QAAG,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,MAAM;QACLgC,kBAAkB,CAACI,IAAI,CAACpC,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdL,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM;IAAEwB,UAAU;IAAEC;EAAY,CAAC,GAAG9C,QAAQ,CAAC,CAAC;EAE9C,oBACEI,OAAA;IAAK2C,SAAS,EAAC,iGAAiG;IAAAC,QAAA,gBAE9G5C,OAAA,CAAC5B,MAAM,CAACyE,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eAE9C5C,OAAA;QAAK2C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B5C,OAAA;UAAK2C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD5C,OAAA,CAAC7B,IAAI;YAAC+E,EAAE,EAAC,GAAG;YAACP,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAClD5C,OAAA,CAAC1B,OAAO;cAACqE,SAAS,EAAC;YAA0B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDtD,OAAA;cAAM2C,SAAS,EAAC,kDAAkD;cAAAC,QAAA,GAAC,OAC5D,eAAA5C,OAAA;gBAAM2C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPtD,OAAA;YAAK2C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5C,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACtB,cAAc,CAAE;cAC/CwC,SAAS,EAAC,UAAU;cAAAC,QAAA,EACrB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACrB,iBAAiB,CAAE;cAClDuC,SAAS,EAAC,UAAU;cAAAC,QAAA,EACrB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACpB,iBAAiB,CAAE;cAClDsC,SAAS,EAAC,UAAU;cAAAC,QAAA,EACrB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAACnB,YAAY,CAAE;cAC7CqC,SAAS,EAAC,UAAU;cAAAC,QAAA,EACrB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNtD,OAAA;YAAK2C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1C5C,OAAA;cACEuD,OAAO,EAAEb,WAAY;cACrBC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,EAEpGH,UAAU,gBAAGzC,OAAA,CAACf,KAAK;gBAAC0D,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGtD,OAAA,CAAChB,MAAM;gBAAC2D,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAGTtD,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5C,OAAA,CAAC7B,IAAI;gBAAC+E,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACf5C,OAAA,CAACH,MAAM;kBAAC2D,OAAO,EAAC,OAAO;kBAACC,IAAI,EAAC,IAAI;kBAAAb,QAAA,EAAC;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACPtD,OAAA,CAAC7B,IAAI;gBAAC+E,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClB5C,OAAA,CAACH,MAAM;kBAAC2D,OAAO,EAAC,SAAS;kBAACC,IAAI,EAAC,IAAI;kBAAAb,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM5C,WAAW,CAAC,CAACD,QAAQ,CAAE;cACtCiC,SAAS,EAAC,qGAAqG;cAAAC,QAAA,EAE9GlC,QAAQ,gBAAGV,OAAA,CAACjB,GAAG;gBAAC4D,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGtD,OAAA,CAAClB,OAAO;gBAAC6D,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL5C,QAAQ,iBACPV,OAAA,CAAC5B,MAAM,CAACsF,GAAG;UACTZ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BY,IAAI,EAAE;YAAEX,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eAEnD5C,OAAA;YAAK2C,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC5C,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM;gBACb9B,eAAe,CAACtB,cAAc,CAAC;gBAC/BQ,WAAW,CAAC,KAAK,CAAC;cACpB,CAAE;cACFgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC/B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM;gBACb9B,eAAe,CAACrB,iBAAiB,CAAC;gBAClCO,WAAW,CAAC,KAAK,CAAC;cACpB,CAAE;cACFgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC/B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM;gBACb9B,eAAe,CAACpB,iBAAiB,CAAC;gBAClCM,WAAW,CAAC,KAAK,CAAC;cACpB,CAAE;cACFgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC/B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM;gBACb9B,eAAe,CAACnB,YAAY,CAAC;gBAC7BK,WAAW,CAAC,KAAK,CAAC;cACpB,CAAE;cACFgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC/B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cAAK2C,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC5C,OAAA,CAAC7B,IAAI;gBAAC+E,EAAE,EAAC,QAAQ;gBAACP,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eAClC5C,OAAA,CAACH,MAAM;kBAAC2D,OAAO,EAAC,OAAO;kBAACC,IAAI,EAAC,IAAI;kBAACd,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAAC;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACPtD,OAAA,CAAC7B,IAAI;gBAAC+E,EAAE,EAAC,WAAW;gBAACP,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACrC5C,OAAA,CAACH,MAAM;kBAAC2D,OAAO,EAAC,SAAS;kBAACC,IAAI,EAAC,IAAI;kBAACd,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEbtD,OAAA;MAAS0B,GAAG,EAAEvB,cAAe;MAACwC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eACrE5C,OAAA;QAAK2C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B5C,OAAA;UAAK2C,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEtD5C,OAAA,CAAC5B,MAAM,CAACsF,GAAG;YACTZ,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCX,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BnB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAErB5C,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5C,OAAA,CAAC5B,MAAM,CAACsF,GAAG;gBACTZ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAG,CAAE;gBAC/BE,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAE,CAAE;gBAC9Bc,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1CpB,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,gBAE9G5C,OAAA,CAACnB,QAAQ;kBAAC8D,SAAS,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uCAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbtD,OAAA;gBAAI2C,SAAS,EAAC,WAAW;gBAAAC,QAAA,GAAC,6BACG,EAAC,GAAG,eAC/B5C,OAAA;kBAAM2C,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC7B5C,OAAA,CAAC3B,0BAA0B;oBAACsE,SAAS,EAAC;kBAAkC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAE7E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAELtD,OAAA;gBAAG2C,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,EAAC;cAIxE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNtD,OAAA,CAAC5B,MAAM,CAACsF,GAAG;cACTZ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9Bc,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CpB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAE3C5C,OAAA,CAAC7B,IAAI;gBAAC+E,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClB5C,OAAA,CAACH,MAAM;kBACL2D,OAAO,EAAC,UAAU;kBAClBC,IAAI,EAAC,IAAI;kBACTd,SAAS,EAAC,kBAAkB;kBAC5BqB,IAAI,eAAEhE,OAAA,CAAC3B,0BAA0B;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACrCW,YAAY,EAAC,OAAO;kBAAArB,QAAA,EACrB;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACPtD,OAAA,CAAC7B,IAAI;gBAAC+E,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACf5C,OAAA,CAACH,MAAM;kBACL2D,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,IAAI;kBACTd,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC7B;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGbtD,OAAA,CAAC5B,MAAM,CAACsF,GAAG;cACTZ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9Bc,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CpB,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAE7D5C,OAAA;gBAAK2C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5C,OAAA,CAACvB,OAAO;kBAACkE,SAAS,EAAC;gBAA0B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDtD,OAAA;kBAAA4C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNtD,OAAA;gBAAK2C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5C,OAAA,CAACtB,MAAM;kBAACiE,SAAS,EAAC;gBAAyB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CtD,OAAA;kBAAA4C,QAAA,EAAM;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNtD,OAAA;gBAAK2C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5C,OAAA,CAACxB,QAAQ;kBAACmE,SAAS,EAAC;gBAA0B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDtD,OAAA;kBAAA4C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbtD,OAAA,CAAC5B,MAAM,CAACsF,GAAG;YACTZ,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAG,CAAE;YAC/BX,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpB5C,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5C,OAAA;gBACEkE,GAAG,EAAEzE,MAAO;gBACZ0E,GAAG,EAAC,mBAAmB;gBACvBxB,SAAS,EAAC;cAAwC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eAGFtD,OAAA,CAAC5B,MAAM,CAACsF,GAAG;gBACTT,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/Bc,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEM,MAAM,EAAEC;gBAAS,CAAE;gBAC9C1B,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,eAEzE5C,OAAA,CAACzB,MAAM;kBAACoE,SAAS,EAAC;gBAA0B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAEbtD,OAAA,CAAC5B,MAAM,CAACsF,GAAG;gBACTT,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9Bc,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEM,MAAM,EAAEC;gBAAS,CAAE;gBAC9C1B,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAE7E5C,OAAA,CAACxB,QAAQ;kBAACmE,SAAS,EAAC;gBAAyB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNtD,OAAA;cAAK2C,SAAS,EAAC;YAAgH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVtD,OAAA;MAAS2C,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAClD5C,OAAA;QAAK2C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B5C,OAAA,CAAC5B,MAAM,CAACsF,GAAG;UACTZ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BuB,WAAW,EAAE;YAAEtB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCc,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB7B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAEhD,CACC;YAAE6B,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,iBAAiB;YAAEV,IAAI,EAAEvF,OAAO;YAAEkG,KAAK,EAAE;UAAgB,CAAC,EAClF;YAAEF,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,gBAAgB;YAAEV,IAAI,EAAEnF,QAAQ;YAAE8F,KAAK,EAAE;UAAiB,CAAC,EACnF;YAAEF,MAAM,EAAE,KAAK;YAAEC,IAAI,EAAE,iBAAiB;YAAEV,IAAI,EAAExF,QAAQ;YAAEmG,KAAK,EAAE;UAAkB,CAAC,EACpF;YAAEF,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,gBAAgB;YAAEV,IAAI,EAAEzF,MAAM;YAAEoG,KAAK,EAAE;UAAkB,CAAC,CACnF,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChB9E,OAAA,CAAC5B,MAAM,CAACsF,GAAG;YAETZ,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCc,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEe,KAAK,GAAG;YAAI,CAAE;YAClDP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B5C,OAAA;cAAK2C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB5C,OAAA;gBAAK2C,SAAS,EAAG,4KAA4K;gBAAAC,QAAA,eAC3L5C,OAAA,CAAC6E,IAAI,CAACb,IAAI;kBAACrB,SAAS,EAAG,WAAUkC,IAAI,CAACF,KAAM;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNtD,OAAA,CAAC5B,MAAM,CAACsF,GAAG;gBACTZ,OAAO,EAAE;kBAAEiC,KAAK,EAAE;gBAAE,CAAE;gBACtBT,WAAW,EAAE;kBAAES,KAAK,EAAE;gBAAE,CAAE;gBAC1BlB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAEe,KAAK,GAAG,GAAG,GAAG;gBAAI,CAAE;gBACxDP,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzB7B,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,eAEzG5C,OAAA;kBAAM2C,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtD,OAAA,CAAC5B,MAAM,CAACsF,GAAG;cACTZ,OAAO,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAE;cACxBsB,WAAW,EAAE;gBAAEtB,OAAO,EAAE;cAAE,CAAE;cAC5Ba,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEe,KAAK,GAAG,GAAG,GAAG;cAAI,CAAE;cACxDP,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB7B,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAE5EiC,IAAI,CAACJ;YAAM;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACbtD,OAAA;cAAG2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EACxDiC,IAAI,CAACH;YAAI;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,GAhCCwB,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiCA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACVtD,OAAA;MAAS0B,GAAG,EAAEtB,iBAAkB;MAACuC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpD5C,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5C,OAAA;UAAK2C,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAuC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpEtD,OAAA;UAAG2C,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAKpB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtD,OAAA;UAAK2C,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5C,OAAA,CAAC7B,IAAI;YAAC+E,EAAE,EAAC,gBAAgB;YAACP,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEhD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtD,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB5C,OAAA;UAAKkE,GAAG,EAAExE,MAAO;UAACyE,GAAG,EAAC,WAAW;UAACxB,SAAS,EAAC;QAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACVtD,OAAA;MAAS0B,GAAG,EAAErB,iBAAkB;MAACsC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpD5C,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB5C,OAAA;UAAK2C,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAC,eACR,eAAA5C,OAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,iBAErB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtD,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBrC,OAAO,CAACyE,MAAM,KAAK,CAAC,GACnBzE,OAAO,CAACqE,GAAG,CAAC,CAACK,MAAM,EAAEH,KAAK;UAAA,IAAAI,YAAA;UAAA,oBACxBlF,OAAA;YAAiB2C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACtC5C,OAAA,CAACZ,IAAI;cAAC+F,YAAY,EAAEF,MAAM,CAACG,MAAO;cAACzC,SAAS,EAAC,MAAM;cAAC0C,QAAQ;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DtD,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAC,IAAC,EAACqC,MAAM,CAACP,IAAI,EAAC,IAAC;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3CtD,OAAA;cAAK2C,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjCtD,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAAsC,YAAA,GAAED,MAAM,CAACK,IAAI,cAAAJ,YAAA,uBAAXA,YAAA,CAAapE;YAAI;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAJvCwB,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CAAC;QAAA,CACP,CAAC,gBAEFtD,OAAA;UAAA4C,QAAA,EAAK;QAAe;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAC1B;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVtD,OAAA;MAAS0B,GAAG,EAAEpB,YAAa;MAACqC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAC/D5C,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB5C,OAAA;UAAK2C,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACNtD,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAC4C,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAA5C,QAAA,eAC9D5C,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B5C,OAAA;YAAM2C,SAAS,EAAC,cAAc;YAAC8C,QAAQ,EAAElD,YAAa;YAAAK,QAAA,gBACpD5C,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5C,OAAA;gBAAO2C,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7CtD,OAAA;gBACE0F,IAAI,EAAC,MAAM;gBACX5E,IAAI,EAAC,MAAM;gBACX6E,WAAW,EAAC,WAAW;gBACvBhD,SAAS,EAAC,eAAe;gBACzBN,KAAK,EAAEzB,QAAQ,CAACE,IAAK;gBACrB8E,QAAQ,EAAEzD,YAAa;gBACvB0D,QAAQ;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtD,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5C,OAAA;gBAAO2C,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9CtD,OAAA;gBACE0F,IAAI,EAAC,OAAO;gBACZ5E,IAAI,EAAC,OAAO;gBACZ6E,WAAW,EAAC,YAAY;gBACxBhD,SAAS,EAAC,eAAe;gBACzBN,KAAK,EAAEzB,QAAQ,CAACG,KAAM;gBACtB6E,QAAQ,EAAEzD,YAAa;gBACvB0D,QAAQ;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtD,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5C,OAAA;gBAAO2C,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChDtD,OAAA;gBACEc,IAAI,EAAC,SAAS;gBACd6E,WAAW,EAAC,cAAc;gBAC1BhD,SAAS,EAAC,kBAAkB;gBAC5B4C,KAAK,EAAE;kBAAEO,KAAK,EAAE,OAAO;kBAAEC,OAAO,EAAE;gBAAO,CAAE;gBAC3C1D,KAAK,EAAEzB,QAAQ,CAACzB,OAAQ;gBACxByG,QAAQ,EAAEzD,YAAa;gBACvB0D,QAAQ;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNtD,OAAA;cACE0F,IAAI,EAAC,QAAQ;cACb/C,SAAS,EAAC,gBAAgB;cAC1B0C,QAAQ,EAAErE,OAAQ;cAAA4B,QAAA,EAEjB5B,OAAO,GAAG,YAAY,GAAG;YAAc;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EACRpC,eAAe,iBACdlB,OAAA;cAAG2C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAE1B;YAAe;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACpD,EAAA,CA3eID,IAAI;EAAA,QAMSZ,WAAW,EAmEQO,QAAQ;AAAA;AAAAoG,EAAA,GAzExC/F,IAAI;AA6eV,eAAeA,IAAI;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}