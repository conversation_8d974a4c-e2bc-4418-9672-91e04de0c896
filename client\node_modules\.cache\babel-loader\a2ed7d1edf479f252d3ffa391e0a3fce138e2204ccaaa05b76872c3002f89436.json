{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizQuestion.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbCheck, TbX, TbClock, TbBulb } from 'react-icons/tb';\nimport { Card, Button } from './index';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizQuestion = ({\n  question,\n  questionNumber,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  showResult = false,\n  correctAnswer = null,\n  timeRemaining = null,\n  isLastQuestion = false,\n  className = ''\n}) => {\n  _s();\n  const [selectedOption, setSelectedOption] = useState(selectedAnswer);\n  useEffect(() => {\n    setSelectedOption(selectedAnswer);\n  }, [selectedAnswer]);\n  const handleOptionSelect = optionIndex => {\n    if (showResult) return; // Prevent selection when showing results\n\n    setSelectedOption(optionIndex);\n    onAnswerSelect(optionIndex);\n  };\n  const getOptionClassName = optionIndex => {\n    const baseClasses = 'quiz-option group cursor-pointer';\n    if (showResult) {\n      if (optionIndex === correctAnswer) {\n        return `${baseClasses} quiz-option-correct cursor-default`;\n      }\n      if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n        return `${baseClasses} quiz-option-incorrect cursor-default`;\n      }\n      return `${baseClasses} opacity-60 cursor-default`;\n    }\n    if (optionIndex === selectedOption) {\n      return `${baseClasses} quiz-option-selected`;\n    }\n    return baseClasses;\n  };\n  const getOptionIcon = optionIndex => {\n    if (!showResult) return null;\n    if (optionIndex === correctAnswer) {\n      return /*#__PURE__*/_jsxDEV(TbCheck, {\n        className: \"w-5 h-5 text-success-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 14\n      }, this);\n    }\n    if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n      return /*#__PURE__*/_jsxDEV(TbX, {\n        className: \"w-5 h-5 text-error-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 14\n      }, this);\n    }\n    return null;\n  };\n  const renderQuestionContent = () => {\n    // Check for image in multiple possible properties\n    const questionImage = question.image || question.imageUrl;\n\n    // Debug logging for image detection\n    if (questionImage) {\n      console.log('Image found for question:', question.name, 'Image URL:', questionImage);\n    }\n    switch (question.type) {\n      case 'image':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [questionImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-image-container-modern\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quiz-image-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionImage,\n                alt: \"Question diagram\",\n                className: \"quiz-image-modern\",\n                onError: e => {\n                  console.error('Image failed to load:', questionImage);\n                  e.target.style.display = 'none';\n                  // Show error message\n                  const errorDiv = document.createElement('div');\n                  errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                  errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                  e.target.parentNode.appendChild(errorDiv);\n                },\n                onLoad: () => {\n                  console.log('Image loaded successfully:', questionImage);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n            children: question.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this);\n      case 'fill':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [questionImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-image-container-modern\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quiz-image-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionImage,\n                alt: \"Question diagram\",\n                className: \"quiz-image-modern\",\n                onError: e => {\n                  console.error('Image failed to load:', questionImage);\n                  e.target.style.display = 'none';\n                  // Show error message\n                  const errorDiv = document.createElement('div');\n                  errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                  errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                  e.target.parentNode.appendChild(errorDiv);\n                },\n                onLoad: () => {\n                  console.log('Image loaded successfully:', questionImage);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n            children: question.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), !showResult ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: selectedOption || '',\n            onChange: e => handleOptionSelect(e.target.value),\n            className: \"input-modern\",\n            placeholder: \"Type your answer here...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Your answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: selectedOption === correctAnswer ? 'text-success-600 font-medium' : 'text-error-600 font-medium',\n                children: selectedOption || 'No answer provided'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-success-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-success-600\",\n                children: \"Correct answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-success-700 font-medium\",\n                children: correctAnswer\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this);\n      default:\n        // MCQ\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [questionImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-image-container-modern\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quiz-image-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionImage,\n                alt: \"Question diagram\",\n                className: \"quiz-image-modern\",\n                onError: e => {\n                  console.error('Image failed to load:', questionImage);\n                  e.target.style.display = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n            children: question.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: question.options && Array.isArray(question.options) && question.options.map((option, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: !showResult ? {\n                scale: 1.02\n              } : {},\n              whileTap: !showResult ? {\n                scale: 0.98\n              } : {},\n              className: getOptionClassName(index),\n              onClick: () => handleOptionSelect(index),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium text-gray-600\",\n                    children: String.fromCharCode(65 + index)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 dark:text-white\",\n                    children: option\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this), getOptionIcon(index)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-medium text-gray-500\",\n          children: [\"Question \", questionNumber, \" of \", totalQuestions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar w-32\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              width: 0\n            },\n            animate: {\n              width: `${questionNumber / totalQuestions * 100}%`\n            },\n            className: \"progress-fill\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), timeRemaining !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex items-center space-x-2 px-3 py-1 rounded-full ${timeRemaining <= 60 ? 'bg-error-100 text-error-700' : 'bg-primary-100 text-primary-700'}`,\n        children: [/*#__PURE__*/_jsxDEV(TbClock, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: [Math.floor(timeRemaining / 60), \":\", (timeRemaining % 60).toString().padStart(2, '0')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"p-8\",\n      children: renderQuestionContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showResult && question.explanation && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          height: 0\n        },\n        animate: {\n          opacity: 1,\n          height: 'auto'\n        },\n        exit: {\n          opacity: 0,\n          height: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6 bg-blue-50 border-blue-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n              className: \"w-6 h-6 text-blue-600 flex-shrink-0 mt-0.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-blue-900 mb-2\",\n                children: \"Explanation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-800 text-sm leading-relaxed\",\n                children: question.explanation\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between pt-6\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onPrevious,\n        disabled: questionNumber === 1,\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: selectedOption !== null && selectedOption !== undefined ? /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-primary-600 font-medium\",\n          children: \"Answer selected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Select an answer to continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: onNext,\n        disabled: !showResult && (selectedOption === null || selectedOption === undefined),\n        children: isLastQuestion ? 'Finish Quiz' : 'Next'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizQuestion, \"4QgR15Umt2Wd5uBhGthegwVI+Tk=\");\n_c = QuizQuestion;\nexport default QuizQuestion;\nvar _c;\n$RefreshReg$(_c, \"QuizQuestion\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "TbCheck", "TbX", "TbClock", "TbBulb", "Card", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizQuestion", "question", "questionNumber", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerSelect", "onNext", "onPrevious", "showResult", "<PERSON><PERSON><PERSON><PERSON>", "timeRemaining", "isLastQuestion", "className", "_s", "selectedOption", "setSelectedOption", "handleOptionSelect", "optionIndex", "getOptionClassName", "baseClasses", "getOptionIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderQuestionContent", "questionImage", "image", "imageUrl", "console", "log", "name", "type", "children", "src", "alt", "onError", "e", "error", "target", "style", "display", "errorDiv", "document", "createElement", "textContent", "parentNode", "append<PERSON><PERSON><PERSON>", "onLoad", "value", "onChange", "placeholder", "options", "Array", "isArray", "map", "option", "index", "div", "whileHover", "scale", "whileTap", "onClick", "String", "fromCharCode", "initial", "width", "animate", "Math", "floor", "toString", "padStart", "explanation", "opacity", "height", "exit", "variant", "disabled", "undefined", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizQuestion.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbCheck, TbX, TbClock, TbBulb } from 'react-icons/tb';\nimport { <PERSON>, Button } from './index';\n\nconst QuizQuestion = ({\n  question,\n  questionNumber,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  showResult = false,\n  correctAnswer = null,\n  timeRemaining = null,\n  isLastQuestion = false,\n  className = '',\n}) => {\n  const [selectedOption, setSelectedOption] = useState(selectedAnswer);\n\n  useEffect(() => {\n    setSelectedOption(selectedAnswer);\n  }, [selectedAnswer]);\n\n  const handleOptionSelect = (optionIndex) => {\n    if (showResult) return; // Prevent selection when showing results\n    \n    setSelectedOption(optionIndex);\n    onAnswerSelect(optionIndex);\n  };\n\n  const getOptionClassName = (optionIndex) => {\n    const baseClasses = 'quiz-option group cursor-pointer';\n    \n    if (showResult) {\n      if (optionIndex === correctAnswer) {\n        return `${baseClasses} quiz-option-correct cursor-default`;\n      }\n      if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n        return `${baseClasses} quiz-option-incorrect cursor-default`;\n      }\n      return `${baseClasses} opacity-60 cursor-default`;\n    }\n    \n    if (optionIndex === selectedOption) {\n      return `${baseClasses} quiz-option-selected`;\n    }\n    \n    return baseClasses;\n  };\n\n  const getOptionIcon = (optionIndex) => {\n    if (!showResult) return null;\n    \n    if (optionIndex === correctAnswer) {\n      return <TbCheck className=\"w-5 h-5 text-success-600\" />;\n    }\n    if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n      return <TbX className=\"w-5 h-5 text-error-600\" />;\n    }\n    return null;\n  };\n\n  const renderQuestionContent = () => {\n    // Check for image in multiple possible properties\n    const questionImage = question.image || question.imageUrl;\n\n    // Debug logging for image detection\n    if (questionImage) {\n      console.log('Image found for question:', question.name, 'Image URL:', questionImage);\n    }\n\n    switch (question.type) {\n      case 'image':\n        return (\n          <div className=\"space-y-6\">\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {question.name}\n            </div>\n          </div>\n        );\n      \n      case 'fill':\n        return (\n          <div className=\"space-y-4\">\n            {/* Show image if available */}\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {question.name}\n            </div>\n            {!showResult ? (\n              <input\n                type=\"text\"\n                value={selectedOption || ''}\n                onChange={(e) => handleOptionSelect(e.target.value)}\n                className=\"input-modern\"\n                placeholder=\"Type your answer here...\"\n              />\n            ) : (\n              <div className=\"space-y-2\">\n                <div className=\"p-4 bg-gray-50 rounded-lg\">\n                  <span className=\"text-sm text-gray-600\">Your answer: </span>\n                  <span className={selectedOption === correctAnswer ? 'text-success-600 font-medium' : 'text-error-600 font-medium'}>\n                    {selectedOption || 'No answer provided'}\n                  </span>\n                </div>\n                <div className=\"p-4 bg-success-50 rounded-lg\">\n                  <span className=\"text-sm text-success-600\">Correct answer: </span>\n                  <span className=\"text-success-700 font-medium\">{correctAnswer}</span>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n      \n      default: // MCQ\n        return (\n          <div className=\"space-y-6\">\n            {/* Show image if available for any question type */}\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {question.name}\n            </div>\n            <div className=\"space-y-3\">\n              {question.options && Array.isArray(question.options) && question.options.map((option, index) => (\n                <motion.div\n                  key={index}\n                  whileHover={!showResult ? { scale: 1.02 } : {}}\n                  whileTap={!showResult ? { scale: 0.98 } : {}}\n                  className={getOptionClassName(index)}\n                  onClick={() => handleOptionSelect(index)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium text-gray-600\">\n                        {String.fromCharCode(65 + index)}\n                      </div>\n                      <span className=\"text-gray-900 dark:text-white\">{option}</span>\n                    </div>\n                    {getOptionIcon(index)}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Question Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"text-sm font-medium text-gray-500\">\n            Question {questionNumber} of {totalQuestions}\n          </div>\n          <div className=\"progress-bar w-32\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${(questionNumber / totalQuestions) * 100}%` }}\n              className=\"progress-fill\"\n            />\n          </div>\n        </div>\n        \n        {timeRemaining !== null && (\n          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${\n            timeRemaining <= 60 ? 'bg-error-100 text-error-700' : 'bg-primary-100 text-primary-700'\n          }`}>\n            <TbClock className=\"w-4 h-4\" />\n            <span className=\"text-sm font-medium\">\n              {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Question Card */}\n      <Card className=\"p-8\">\n        {renderQuestionContent()}\n      </Card>\n\n      {/* Explanation (shown after answer) */}\n      <AnimatePresence>\n        {showResult && question.explanation && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n          >\n            <Card className=\"p-6 bg-blue-50 border-blue-200\">\n              <div className=\"flex items-start space-x-3\">\n                <TbBulb className=\"w-6 h-6 text-blue-600 flex-shrink-0 mt-0.5\" />\n                <div>\n                  <h4 className=\"font-medium text-blue-900 mb-2\">Explanation</h4>\n                  <p className=\"text-blue-800 text-sm leading-relaxed\">\n                    {question.explanation}\n                  </p>\n                </div>\n              </div>\n            </Card>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Navigation */}\n      <div className=\"flex items-center justify-between pt-6\">\n        <Button\n          variant=\"secondary\"\n          onClick={onPrevious}\n          disabled={questionNumber === 1}\n        >\n          Previous\n        </Button>\n        \n        <div className=\"text-sm text-gray-500\">\n          {selectedOption !== null && selectedOption !== undefined ? (\n            <span className=\"text-primary-600 font-medium\">Answer selected</span>\n          ) : (\n            <span>Select an answer to continue</span>\n          )}\n        </div>\n        \n        <Button\n          variant=\"primary\"\n          onClick={onNext}\n          disabled={!showResult && (selectedOption === null || selectedOption === undefined)}\n        >\n          {isLastQuestion ? 'Finish Quiz' : 'Next'}\n        </Button>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizQuestion;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AAC9D,SAASC,IAAI,EAAEC,MAAM,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,MAAM;EACNC,UAAU;EACVC,UAAU,GAAG,KAAK;EAClBC,aAAa,GAAG,IAAI;EACpBC,aAAa,GAAG,IAAI;EACpBC,cAAc,GAAG,KAAK;EACtBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAACgB,cAAc,CAAC;EAEpEf,SAAS,CAAC,MAAM;IACd0B,iBAAiB,CAACX,cAAc,CAAC;EACnC,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAMY,kBAAkB,GAAIC,WAAW,IAAK;IAC1C,IAAIT,UAAU,EAAE,OAAO,CAAC;;IAExBO,iBAAiB,CAACE,WAAW,CAAC;IAC9BZ,cAAc,CAACY,WAAW,CAAC;EAC7B,CAAC;EAED,MAAMC,kBAAkB,GAAID,WAAW,IAAK;IAC1C,MAAME,WAAW,GAAG,kCAAkC;IAEtD,IAAIX,UAAU,EAAE;MACd,IAAIS,WAAW,KAAKR,aAAa,EAAE;QACjC,OAAQ,GAAEU,WAAY,qCAAoC;MAC5D;MACA,IAAIF,WAAW,KAAKH,cAAc,IAAIG,WAAW,KAAKR,aAAa,EAAE;QACnE,OAAQ,GAAEU,WAAY,uCAAsC;MAC9D;MACA,OAAQ,GAAEA,WAAY,4BAA2B;IACnD;IAEA,IAAIF,WAAW,KAAKH,cAAc,EAAE;MAClC,OAAQ,GAAEK,WAAY,uBAAsB;IAC9C;IAEA,OAAOA,WAAW;EACpB,CAAC;EAED,MAAMC,aAAa,GAAIH,WAAW,IAAK;IACrC,IAAI,CAACT,UAAU,EAAE,OAAO,IAAI;IAE5B,IAAIS,WAAW,KAAKR,aAAa,EAAE;MACjC,oBAAOV,OAAA,CAACP,OAAO;QAACoB,SAAS,EAAC;MAA0B;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzD;IACA,IAAIP,WAAW,KAAKH,cAAc,IAAIG,WAAW,KAAKR,aAAa,EAAE;MACnE,oBAAOV,OAAA,CAACN,GAAG;QAACmB,SAAS,EAAC;MAAwB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACnD;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMC,aAAa,GAAGzB,QAAQ,CAAC0B,KAAK,IAAI1B,QAAQ,CAAC2B,QAAQ;;IAEzD;IACA,IAAIF,aAAa,EAAE;MACjBG,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE7B,QAAQ,CAAC8B,IAAI,EAAE,YAAY,EAAEL,aAAa,CAAC;IACtF;IAEA,QAAQzB,QAAQ,CAAC+B,IAAI;MACnB,KAAK,OAAO;QACV,oBACEjC,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAqB,QAAA,GACvBP,aAAa,iBACZ3B,OAAA;YAAKa,SAAS,EAAC,6BAA6B;YAAAqB,QAAA,eAC1ClC,OAAA;cAAKa,SAAS,EAAC,oBAAoB;cAAAqB,QAAA,eACjClC,OAAA;gBACEmC,GAAG,EAAER,aAAc;gBACnBS,GAAG,EAAC,kBAAkB;gBACtBvB,SAAS,EAAC,mBAAmB;gBAC7BwB,OAAO,EAAGC,CAAC,IAAK;kBACdR,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEZ,aAAa,CAAC;kBACrDW,CAAC,CAACE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;kBAC/B;kBACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;kBAC9CF,QAAQ,CAAC9B,SAAS,GAAG,iFAAiF;kBACtG8B,QAAQ,CAACG,WAAW,GAAG,wDAAwD;kBAC/ER,CAAC,CAACE,MAAM,CAACO,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC;gBAC3C,CAAE;gBACFM,MAAM,EAAEA,CAAA,KAAM;kBACZnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEJ,aAAa,CAAC;gBAC1D;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACDzB,OAAA;YAAKa,SAAS,EAAC,mDAAmD;YAAAqB,QAAA,EAC/DhC,QAAQ,CAAC8B;UAAI;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,MAAM;QACT,oBACEzB,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAqB,QAAA,GAEvBP,aAAa,iBACZ3B,OAAA;YAAKa,SAAS,EAAC,6BAA6B;YAAAqB,QAAA,eAC1ClC,OAAA;cAAKa,SAAS,EAAC,oBAAoB;cAAAqB,QAAA,eACjClC,OAAA;gBACEmC,GAAG,EAAER,aAAc;gBACnBS,GAAG,EAAC,kBAAkB;gBACtBvB,SAAS,EAAC,mBAAmB;gBAC7BwB,OAAO,EAAGC,CAAC,IAAK;kBACdR,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEZ,aAAa,CAAC;kBACrDW,CAAC,CAACE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;kBAC/B;kBACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;kBAC9CF,QAAQ,CAAC9B,SAAS,GAAG,iFAAiF;kBACtG8B,QAAQ,CAACG,WAAW,GAAG,wDAAwD;kBAC/ER,CAAC,CAACE,MAAM,CAACO,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC;gBAC3C,CAAE;gBACFM,MAAM,EAAEA,CAAA,KAAM;kBACZnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEJ,aAAa,CAAC;gBAC1D;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACDzB,OAAA;YAAKa,SAAS,EAAC,mDAAmD;YAAAqB,QAAA,EAC/DhC,QAAQ,CAAC8B;UAAI;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EACL,CAAChB,UAAU,gBACVT,OAAA;YACEiC,IAAI,EAAC,MAAM;YACXiB,KAAK,EAAEnC,cAAc,IAAI,EAAG;YAC5BoC,QAAQ,EAAGb,CAAC,IAAKrB,kBAAkB,CAACqB,CAAC,CAACE,MAAM,CAACU,KAAK,CAAE;YACpDrC,SAAS,EAAC,cAAc;YACxBuC,WAAW,EAAC;UAA0B;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,gBAEFzB,OAAA;YAAKa,SAAS,EAAC,WAAW;YAAAqB,QAAA,gBACxBlC,OAAA;cAAKa,SAAS,EAAC,2BAA2B;cAAAqB,QAAA,gBACxClC,OAAA;gBAAMa,SAAS,EAAC,uBAAuB;gBAAAqB,QAAA,EAAC;cAAa;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5DzB,OAAA;gBAAMa,SAAS,EAAEE,cAAc,KAAKL,aAAa,GAAG,8BAA8B,GAAG,4BAA6B;gBAAAwB,QAAA,EAC/GnB,cAAc,IAAI;cAAoB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNzB,OAAA;cAAKa,SAAS,EAAC,8BAA8B;cAAAqB,QAAA,gBAC3ClC,OAAA;gBAAMa,SAAS,EAAC,0BAA0B;gBAAAqB,QAAA,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClEzB,OAAA;gBAAMa,SAAS,EAAC,8BAA8B;gBAAAqB,QAAA,EAAExB;cAAa;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV;QAAS;QACP,oBACEzB,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAqB,QAAA,GAEvBP,aAAa,iBACZ3B,OAAA;YAAKa,SAAS,EAAC,6BAA6B;YAAAqB,QAAA,eAC1ClC,OAAA;cAAKa,SAAS,EAAC,oBAAoB;cAAAqB,QAAA,eACjClC,OAAA;gBACEmC,GAAG,EAAER,aAAc;gBACnBS,GAAG,EAAC,kBAAkB;gBACtBvB,SAAS,EAAC,mBAAmB;gBAC7BwB,OAAO,EAAGC,CAAC,IAAK;kBACdR,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEZ,aAAa,CAAC;kBACrDW,CAAC,CAACE,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;gBACjC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACDzB,OAAA;YAAKa,SAAS,EAAC,mDAAmD;YAAAqB,QAAA,EAC/DhC,QAAQ,CAAC8B;UAAI;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNzB,OAAA;YAAKa,SAAS,EAAC,WAAW;YAAAqB,QAAA,EACvBhC,QAAQ,CAACmD,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACrD,QAAQ,CAACmD,OAAO,CAAC,IAAInD,QAAQ,CAACmD,OAAO,CAACG,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzF1D,OAAA,CAACT,MAAM,CAACoE,GAAG;cAETC,UAAU,EAAE,CAACnD,UAAU,GAAG;gBAAEoD,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAC/CC,QAAQ,EAAE,CAACrD,UAAU,GAAG;gBAAEoD,KAAK,EAAE;cAAK,CAAC,GAAG,CAAC,CAAE;cAC7ChD,SAAS,EAAEM,kBAAkB,CAACuC,KAAK,CAAE;cACrCK,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACyC,KAAK,CAAE;cAAAxB,QAAA,eAEzClC,OAAA;gBAAKa,SAAS,EAAC,mCAAmC;gBAAAqB,QAAA,gBAChDlC,OAAA;kBAAKa,SAAS,EAAC,6BAA6B;kBAAAqB,QAAA,gBAC1ClC,OAAA;oBAAKa,SAAS,EAAC,mHAAmH;oBAAAqB,QAAA,EAC/H8B,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGP,KAAK;kBAAC;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACNzB,OAAA;oBAAMa,SAAS,EAAC,+BAA+B;oBAAAqB,QAAA,EAAEuB;kBAAM;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,EACLJ,aAAa,CAACqC,KAAK,CAAC;cAAA;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC,GAdDiC,KAAK;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;IAEZ;EACF,CAAC;EAED,oBACEzB,OAAA;IAAKa,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAAqB,QAAA,gBAEvClC,OAAA;MAAKa,SAAS,EAAC,mCAAmC;MAAAqB,QAAA,gBAChDlC,OAAA;QAAKa,SAAS,EAAC,6BAA6B;QAAAqB,QAAA,gBAC1ClC,OAAA;UAAKa,SAAS,EAAC,mCAAmC;UAAAqB,QAAA,GAAC,WACxC,EAAC/B,cAAc,EAAC,MAAI,EAACC,cAAc;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNzB,OAAA;UAAKa,SAAS,EAAC,mBAAmB;UAAAqB,QAAA,eAChClC,OAAA,CAACT,MAAM,CAACoE,GAAG;YACTO,OAAO,EAAE;cAAEC,KAAK,EAAE;YAAE,CAAE;YACtBC,OAAO,EAAE;cAAED,KAAK,EAAG,GAAGhE,cAAc,GAAGC,cAAc,GAAI,GAAI;YAAG,CAAE;YAClES,SAAS,EAAC;UAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELd,aAAa,KAAK,IAAI,iBACrBX,OAAA;QAAKa,SAAS,EAAG,sDACfF,aAAa,IAAI,EAAE,GAAG,6BAA6B,GAAG,iCACvD,EAAE;QAAAuB,QAAA,gBACDlC,OAAA,CAACL,OAAO;UAACkB,SAAS,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BzB,OAAA;UAAMa,SAAS,EAAC,qBAAqB;UAAAqB,QAAA,GAClCmC,IAAI,CAACC,KAAK,CAAC3D,aAAa,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,aAAa,GAAG,EAAE,EAAE4D,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAAA;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzB,OAAA,CAACH,IAAI;MAACgB,SAAS,EAAC,KAAK;MAAAqB,QAAA,EAClBR,qBAAqB,CAAC;IAAC;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGPzB,OAAA,CAACR,eAAe;MAAA0C,QAAA,EACbzB,UAAU,IAAIP,QAAQ,CAACuE,WAAW,iBACjCzE,OAAA,CAACT,MAAM,CAACoE,GAAG;QACTO,OAAO,EAAE;UAAEQ,OAAO,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAE;QACnCP,OAAO,EAAE;UAAEM,OAAO,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAO,CAAE;QACxCC,IAAI,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAzC,QAAA,eAEhClC,OAAA,CAACH,IAAI;UAACgB,SAAS,EAAC,gCAAgC;UAAAqB,QAAA,eAC9ClC,OAAA;YAAKa,SAAS,EAAC,4BAA4B;YAAAqB,QAAA,gBACzClC,OAAA,CAACJ,MAAM;cAACiB,SAAS,EAAC;YAA4C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjEzB,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAIa,SAAS,EAAC,gCAAgC;gBAAAqB,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DzB,OAAA;gBAAGa,SAAS,EAAC,uCAAuC;gBAAAqB,QAAA,EACjDhC,QAAQ,CAACuE;cAAW;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBzB,OAAA;MAAKa,SAAS,EAAC,wCAAwC;MAAAqB,QAAA,gBACrDlC,OAAA,CAACF,MAAM;QACL+E,OAAO,EAAC,WAAW;QACnBd,OAAO,EAAEvD,UAAW;QACpBsE,QAAQ,EAAE3E,cAAc,KAAK,CAAE;QAAA+B,QAAA,EAChC;MAED;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETzB,OAAA;QAAKa,SAAS,EAAC,uBAAuB;QAAAqB,QAAA,EACnCnB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKgE,SAAS,gBACtD/E,OAAA;UAAMa,SAAS,EAAC,8BAA8B;UAAAqB,QAAA,EAAC;QAAe;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAErEzB,OAAA;UAAAkC,QAAA,EAAM;QAA4B;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACzC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENzB,OAAA,CAACF,MAAM;QACL+E,OAAO,EAAC,SAAS;QACjBd,OAAO,EAAExD,MAAO;QAChBuE,QAAQ,EAAE,CAACrE,UAAU,KAAKM,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKgE,SAAS,CAAE;QAAA7C,QAAA,EAElFtB,cAAc,GAAG,aAAa,GAAG;MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CAjSIb,YAAY;AAAA+E,EAAA,GAAZ/E,YAAY;AAmSlB,eAAeA,YAAY;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}