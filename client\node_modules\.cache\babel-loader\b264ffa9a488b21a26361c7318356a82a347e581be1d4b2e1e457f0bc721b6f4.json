{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Register\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Select } from \"antd\"; // Added Select for dropdown\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Register() {\n  _s();\n  const [verification, setVerification] = useState(false);\n  const [data, setData] = useState(\"\");\n  const [otp, setOTP] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\"); // State to store selected school type\n  const navigate = useNavigate();\n  const onFinish = async values => {\n    try {\n      const response = await registerUser(values);\n      if (response.success) {\n        message.success(response.message);\n        navigate(\"/login\");\n      } else {\n        message.error(response.message);\n        setVerification(false);\n      }\n    } catch (error) {\n      message.error(error.message);\n      setVerification(false);\n    }\n    console.log(values);\n  };\n  const verifyUser = async values => {\n    if (values.otp === otp) {\n      onFinish(data);\n    } else {\n      message.error(\"Invalid OTP\");\n    }\n  };\n  const generateOTP = async formData => {\n    if (!formData.name || !formData.email || !formData.password) {\n      message.error(\"Please fill all fields!\");\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await sendOTP(formData);\n      if (response.success) {\n        message.success(response.message);\n        setData(formData);\n        setOTP(response.data);\n        setVerification(true);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    setLoading(false);\n  };\n  const handleSchoolTypeChange = value => {\n    setSchoolType(value); // Update the state with selected school type\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\",\n        children: verification ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-shield-check-line text-2xl text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900 mb-2\",\n              children: \"Verify Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Enter the OTP sent to your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            layout: \"vertical\",\n            onFinish: verifyUser,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"otp\",\n              label: \"OTP Code\",\n              initialValue: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-center text-lg tracking-widest\",\n                placeholder: \"Enter 6-digit OTP\",\n                maxLength: \"6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\",\n              children: \"Verify & Complete Registration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-user-add-line text-2xl text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900 mb-2\",\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Join thousands of students learning with Brainwave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            layout: \"vertical\",\n            onFinish: generateOTP,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"Full Name\",\n              initialValue: \"\",\n              rules: [{\n                required: true,\n                message: \"Please enter your name!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n                placeholder: \"Enter your full name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"school\",\n              label: \"School\",\n              initialValue: \"\",\n              rules: [{\n                required: true,\n                message: \"Please enter your school!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n                placeholder: \"Enter your school name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"Education Level\",\n              initialValue: \"\",\n              rules: [{\n                required: true,\n                message: \"Please select your level!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                onChange: e => setSchoolType(e.target.value),\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  selected: true,\n                  children: \"Select Education Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Primary\",\n                  children: \"Primary Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Secondary\",\n                  children: \"Secondary Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Advance\",\n                  children: \"Advanced Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"class\",\n              label: \"Class\",\n              initialValue: \"\",\n              rules: [{\n                required: true,\n                message: \"Please select your class!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  selected: true,\n                  children: \"Select Your Class\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this), schoolType === \"Primary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"1\",\n                    children: \"Class 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"2\",\n                    children: \"Class 2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"3\",\n                    children: \"Class 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"4\",\n                    children: \"Class 4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"5\",\n                    children: \"Class 5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"6\",\n                    children: \"Class 6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"7\",\n                    children: \"Class 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true), schoolType === \"Secondary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Form-1\",\n                    children: \"Form 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Form-2\",\n                    children: \"Form 2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Form-3\",\n                    children: \"Form 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Form-4\",\n                    children: \"Form 4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true), schoolType === \"Advance\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Form-5\",\n                    children: \"Form 5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Form-6\",\n                    children: \"Form 6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"email\",\n              label: \"Email Address\",\n              initialValue: \"\",\n              rules: [{\n                required: true,\n                message: \"Please enter your email!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n                placeholder: \"Enter your email address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phoneNumber\",\n              label: \"Phone Number\",\n              initialValue: \"\",\n              rules: [{\n                required: true,\n                message: \"Please enter your phone number!\"\n              }, {\n                pattern: /^\\d{10}$/,\n                message: \"Phone number must be exactly 10 digits!\"\n              }],\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                maxLength: \"10\",\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n                placeholder: \"Enter 10-digit phone number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: \"Used for payment verification\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              label: \"Password\",\n              initialValue: \"\",\n              rules: [{\n                required: true,\n                message: \"Please enter your password!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\",\n                placeholder: \"Create a strong password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                disabled: loading,\n                children: loading ? 'Creating Account...' : 'Create Account'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200\",\n                  children: \"Already have an account? Sign in\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"ZCOX0z1U6pPkSU7THaoXima+72A=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["Form", "message", "Select", "React", "useState", "Link", "useNavigate", "registerUser", "sendOTP", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "verification", "setVerification", "data", "setData", "otp", "setOTP", "loading", "setLoading", "schoolType", "setSchoolType", "navigate", "onFinish", "values", "response", "success", "error", "console", "log", "verifyUser", "generateOTP", "formData", "name", "email", "password", "handleSchoolTypeChange", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "label", "initialValue", "type", "placeholder", "max<PERSON><PERSON><PERSON>", "rules", "required", "onChange", "e", "target", "disabled", "selected", "pattern", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Register/index.js"], "sourcesContent": ["import { Form, message, Select } from \"antd\"; // Added Select for dropdown\r\nimport React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\r\n\r\nfunction Register() {\r\n  const [verification, setVerification] = useState(false);\r\n  const [data, setData] = useState(\"\");\r\n  const [otp, setOTP] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [schoolType, setSchoolType] = useState(\"\"); // State to store selected school type\r\n  const navigate = useNavigate();\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      const response = await registerUser(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        navigate(\"/login\");\r\n      } else {\r\n        message.error(response.message);\r\n        setVerification(false);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      setVerification(false);\r\n    }\r\n    console.log(values);\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === otp) {\r\n      onFinish(data);\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  const generateOTP = async (formData) => {\r\n    if (!formData.name || !formData.email || !formData.password) {\r\n      message.error(\"Please fill all fields!\");\r\n      return;\r\n    }\r\n    setLoading(true);\r\n    try {\r\n      const response = await sendOTP(formData);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setData(formData);\r\n        setOTP(response.data);\r\n        setVerification(true);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const handleSchoolTypeChange = (value) => {\r\n    setSchoolType(value); // Update the state with selected school type\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4\">\r\n      <div className=\"w-full max-w-md\">\r\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\r\n          {verification ? (\r\n            <div>\r\n              <div className=\"text-center mb-8\">\r\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <i className=\"ri-shield-check-line text-2xl text-blue-600\"></i>\r\n                </div>\r\n                <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Verify Your Email</h1>\r\n                <p className=\"text-gray-600\">Enter the OTP sent to your email</p>\r\n              </div>\r\n\r\n              <Form layout=\"vertical\" onFinish={verifyUser} className=\"space-y-6\">\r\n                <Form.Item name=\"otp\" label=\"OTP Code\" initialValue=\"\">\r\n                  <input\r\n                    type=\"number\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-center text-lg tracking-widest\"\r\n                    placeholder=\"Enter 6-digit OTP\"\r\n                    maxLength=\"6\"\r\n                  />\r\n                </Form.Item>\r\n\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\"\r\n                >\r\n                  Verify & Complete Registration\r\n                </button>\r\n              </Form>\r\n            </div>\r\n          ) : (\r\n            <div>\r\n              <div className=\"text-center mb-8\">\r\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <i className=\"ri-user-add-line text-2xl text-blue-600\"></i>\r\n                </div>\r\n                <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Create Account</h1>\r\n                <p className=\"text-gray-600\">Join thousands of students learning with Brainwave</p>\r\n              </div>\r\n\r\n            <Form layout=\"vertical\" onFinish={generateOTP} className=\"space-y-4\">\r\n              <Form.Item name=\"name\" label=\"Full Name\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your name!\" }]}>\r\n                <input\r\n                  type=\"text\"\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                  placeholder=\"Enter your full name\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"school\" label=\"School\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your school!\" }]}>\r\n                <input\r\n                  type=\"text\"\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                  placeholder=\"Enter your school name\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"level\" label=\"Education Level\" initialValue=\"\" rules={[{ required: true, message: \"Please select your level!\" }]}>\r\n                <select\r\n                  onChange={(e) => setSchoolType(e.target.value)}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                >\r\n                  <option value=\"\" disabled selected>\r\n                    Select Education Level\r\n                  </option>\r\n                  <option value=\"Primary\">Primary Education</option>\r\n                  <option value=\"Secondary\">Secondary Education</option>\r\n                  <option value=\"Advance\">Advanced Level</option>\r\n                </select>\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" rules={[{ required: true, message: \"Please select your class!\" }]}>\r\n                <select className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\">\r\n                  <option value=\"\" disabled selected>\r\n                    Select Your Class\r\n                  </option>\r\n                  {schoolType === \"Primary\" && (\r\n                    <>\r\n                      <option value=\"1\">Class 1</option>\r\n                      <option value=\"2\">Class 2</option>\r\n                      <option value=\"3\">Class 3</option>\r\n                      <option value=\"4\">Class 4</option>\r\n                      <option value=\"5\">Class 5</option>\r\n                      <option value=\"6\">Class 6</option>\r\n                      <option value=\"7\">Class 7</option>\r\n                    </>\r\n                  )}\r\n                  {schoolType === \"Secondary\" && (\r\n                    <>\r\n                      <option value=\"Form-1\">Form 1</option>\r\n                      <option value=\"Form-2\">Form 2</option>\r\n                      <option value=\"Form-3\">Form 3</option>\r\n                      <option value=\"Form-4\">Form 4</option>\r\n                    </>\r\n                  )}\r\n                  {schoolType === \"Advance\" && (\r\n                    <>\r\n                      <option value=\"Form-5\">Form 5</option>\r\n                      <option value=\"Form-6\">Form 6</option>\r\n                    </>\r\n                  )}\r\n                </select>\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"email\" label=\"Email Address\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your email!\" }]}>\r\n                <input\r\n                  type=\"email\"\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                  placeholder=\"Enter your email address\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phoneNumber\"\r\n                label=\"Phone Number\"\r\n                initialValue=\"\"\r\n                rules={[\r\n                  {\r\n                    required: true,\r\n                    message: \"Please enter your phone number!\",\r\n                  },\r\n                  {\r\n                    pattern: /^\\d{10}$/,\r\n                    message: \"Phone number must be exactly 10 digits!\",\r\n                  },\r\n                ]}\r\n              >\r\n                <input\r\n                  type=\"tel\"\r\n                  maxLength=\"10\"\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                  placeholder=\"Enter 10-digit phone number\"\r\n                />\r\n                <p className=\"text-sm text-gray-500 mt-1\">Used for payment verification</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"password\" label=\"Password\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your password!\" }]}>\r\n                <input\r\n                  type=\"password\"\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white\"\r\n                  placeholder=\"Create a strong password\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <div className=\"space-y-4\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  disabled={loading}\r\n                >\r\n                  {loading ? 'Creating Account...' : 'Create Account'}\r\n                </button>\r\n\r\n                <div className=\"text-center\">\r\n                  <Link\r\n                    to=\"/login\"\r\n                    className=\"text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200\"\r\n                  >\r\n                    Already have an account? Sign in\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </Form>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,MAAM,QAAQ,MAAM,CAAC,CAAC;AAC9C,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhE,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACa,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACe,GAAG,EAAEC,MAAM,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAMqB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,YAAY,CAACoB,MAAM,CAAC;MAC3C,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB5B,OAAO,CAAC4B,OAAO,CAACD,QAAQ,CAAC3B,OAAO,CAAC;QACjCwB,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLxB,OAAO,CAAC6B,KAAK,CAACF,QAAQ,CAAC3B,OAAO,CAAC;QAC/Be,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAACA,KAAK,CAAC7B,OAAO,CAAC;MAC5Be,eAAe,CAAC,KAAK,CAAC;IACxB;IACAe,OAAO,CAACC,GAAG,CAACL,MAAM,CAAC;EACrB,CAAC;EAED,MAAMM,UAAU,GAAG,MAAON,MAAM,IAAK;IACnC,IAAIA,MAAM,CAACR,GAAG,KAAKA,GAAG,EAAE;MACtBO,QAAQ,CAACT,IAAI,CAAC;IAChB,CAAC,MAAM;MACLhB,OAAO,CAAC6B,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;EAED,MAAMI,WAAW,GAAG,MAAOC,QAAQ,IAAK;IACtC,IAAI,CAACA,QAAQ,CAACC,IAAI,IAAI,CAACD,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MAC3DrC,OAAO,CAAC6B,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF;IACAR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMpB,OAAO,CAAC2B,QAAQ,CAAC;MACxC,IAAIP,QAAQ,CAACC,OAAO,EAAE;QACpB5B,OAAO,CAAC4B,OAAO,CAACD,QAAQ,CAAC3B,OAAO,CAAC;QACjCiB,OAAO,CAACiB,QAAQ,CAAC;QACjBf,MAAM,CAACQ,QAAQ,CAACX,IAAI,CAAC;QACrBD,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACLf,OAAO,CAAC6B,KAAK,CAACF,QAAQ,CAAC3B,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAACA,KAAK,CAAC7B,OAAO,CAAC;IAC9B;IACAqB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMiB,sBAAsB,GAAIC,KAAK,IAAK;IACxChB,aAAa,CAACgB,KAAK,CAAC,CAAC,CAAC;EACxB,CAAC;;EAED,oBACE9B,OAAA;IAAK+B,SAAS,EAAC,8FAA8F;IAAAC,QAAA,eAC3GhC,OAAA;MAAK+B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BhC,OAAA;QAAK+B,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EACvE3B,YAAY,gBACXL,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAK+B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BhC,OAAA;cAAK+B,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FhC,OAAA;gBAAG+B,SAAS,EAAC;cAA6C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNpC,OAAA;cAAI+B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EpC,OAAA;cAAG+B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAENpC,OAAA,CAACV,IAAI;YAAC+C,MAAM,EAAC,UAAU;YAACrB,QAAQ,EAAEO,UAAW;YAACQ,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjEhC,OAAA,CAACV,IAAI,CAACgD,IAAI;cAACZ,IAAI,EAAC,KAAK;cAACa,KAAK,EAAC,UAAU;cAACC,YAAY,EAAC,EAAE;cAAAR,QAAA,eACpDhC,OAAA;gBACEyC,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,wMAAwM;gBAClNW,WAAW,EAAC,mBAAmB;gBAC/BC,SAAS,EAAC;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZpC,OAAA;cACEyC,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,oKAAoK;cAAAC,QAAA,EAC/K;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAENpC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAK+B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BhC,OAAA;cAAK+B,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FhC,OAAA;gBAAG+B,SAAS,EAAC;cAAyC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNpC,OAAA;cAAI+B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEpC,OAAA;cAAG+B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAkD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eAERpC,OAAA,CAACV,IAAI;YAAC+C,MAAM,EAAC,UAAU;YAACrB,QAAQ,EAAEQ,WAAY;YAACO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAClEhC,OAAA,CAACV,IAAI,CAACgD,IAAI;cAACZ,IAAI,EAAC,MAAM;cAACa,KAAK,EAAC,WAAW;cAACC,YAAY,EAAC,EAAE;cAACI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtD,OAAO,EAAE;cAA0B,CAAC,CAAE;cAAAyC,QAAA,eACvHhC,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,oKAAoK;gBAC9KW,WAAW,EAAC;cAAsB;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZpC,OAAA,CAACV,IAAI,CAACgD,IAAI;cAACZ,IAAI,EAAC,QAAQ;cAACa,KAAK,EAAC,QAAQ;cAACC,YAAY,EAAC,EAAE;cAACI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtD,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAAyC,QAAA,eACxHhC,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,oKAAoK;gBAC9KW,WAAW,EAAC;cAAwB;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZpC,OAAA,CAACV,IAAI,CAACgD,IAAI;cAACZ,IAAI,EAAC,OAAO;cAACa,KAAK,EAAC,iBAAiB;cAACC,YAAY,EAAC,EAAE;cAACI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtD,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAAyC,QAAA,eAChIhC,OAAA;gBACE8C,QAAQ,EAAGC,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;gBAC/CC,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,gBAE9KhC,OAAA;kBAAQ8B,KAAK,EAAC,EAAE;kBAACmB,QAAQ;kBAACC,QAAQ;kBAAAlB,QAAA,EAAC;gBAEnC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpC,OAAA;kBAAQ8B,KAAK,EAAC,SAAS;kBAAAE,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClDpC,OAAA;kBAAQ8B,KAAK,EAAC,WAAW;kBAAAE,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDpC,OAAA;kBAAQ8B,KAAK,EAAC,SAAS;kBAAAE,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZpC,OAAA,CAACV,IAAI,CAACgD,IAAI;cAACZ,IAAI,EAAC,OAAO;cAACa,KAAK,EAAC,OAAO;cAACC,YAAY,EAAC,EAAE;cAACI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtD,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAAyC,QAAA,eACtHhC,OAAA;gBAAQ+B,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,gBACpLhC,OAAA;kBAAQ8B,KAAK,EAAC,EAAE;kBAACmB,QAAQ;kBAACC,QAAQ;kBAAAlB,QAAA,EAAC;gBAEnC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRvB,UAAU,KAAK,SAAS,iBACvBb,OAAA,CAAAE,SAAA;kBAAA8B,QAAA,gBACEhC,OAAA;oBAAQ8B,KAAK,EAAC,GAAG;oBAAAE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCpC,OAAA;oBAAQ8B,KAAK,EAAC,GAAG;oBAAAE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCpC,OAAA;oBAAQ8B,KAAK,EAAC,GAAG;oBAAAE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCpC,OAAA;oBAAQ8B,KAAK,EAAC,GAAG;oBAAAE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCpC,OAAA;oBAAQ8B,KAAK,EAAC,GAAG;oBAAAE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCpC,OAAA;oBAAQ8B,KAAK,EAAC,GAAG;oBAAAE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCpC,OAAA;oBAAQ8B,KAAK,EAAC,GAAG;oBAAAE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eAClC,CACH,EACAvB,UAAU,KAAK,WAAW,iBACzBb,OAAA,CAAAE,SAAA;kBAAA8B,QAAA,gBACEhC,OAAA;oBAAQ8B,KAAK,EAAC,QAAQ;oBAAAE,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCpC,OAAA;oBAAQ8B,KAAK,EAAC,QAAQ;oBAAAE,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCpC,OAAA;oBAAQ8B,KAAK,EAAC,QAAQ;oBAAAE,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCpC,OAAA;oBAAQ8B,KAAK,EAAC,QAAQ;oBAAAE,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACtC,CACH,EACAvB,UAAU,KAAK,SAAS,iBACvBb,OAAA,CAAAE,SAAA;kBAAA8B,QAAA,gBACEhC,OAAA;oBAAQ8B,KAAK,EAAC,QAAQ;oBAAAE,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCpC,OAAA;oBAAQ8B,KAAK,EAAC,QAAQ;oBAAAE,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACtC,CACH;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZpC,OAAA,CAACV,IAAI,CAACgD,IAAI;cAACZ,IAAI,EAAC,OAAO;cAACa,KAAK,EAAC,eAAe;cAACC,YAAY,EAAC,EAAE;cAACI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtD,OAAO,EAAE;cAA2B,CAAC,CAAE;cAAAyC,QAAA,eAC7HhC,OAAA;gBACEyC,IAAI,EAAC,OAAO;gBACZV,SAAS,EAAC,oKAAoK;gBAC9KW,WAAW,EAAC;cAA0B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZpC,OAAA,CAACV,IAAI,CAACgD,IAAI;cACRZ,IAAI,EAAC,aAAa;cAClBa,KAAK,EAAC,cAAc;cACpBC,YAAY,EAAC,EAAE;cACfI,KAAK,EAAE,CACL;gBACEC,QAAQ,EAAE,IAAI;gBACdtD,OAAO,EAAE;cACX,CAAC,EACD;gBACE4D,OAAO,EAAE,UAAU;gBACnB5D,OAAO,EAAE;cACX,CAAC,CACD;cAAAyC,QAAA,gBAEFhC,OAAA;gBACEyC,IAAI,EAAC,KAAK;gBACVE,SAAS,EAAC,IAAI;gBACdZ,SAAS,EAAC,oKAAoK;gBAC9KW,WAAW,EAAC;cAA6B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACFpC,OAAA;gBAAG+B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eAEZpC,OAAA,CAACV,IAAI,CAACgD,IAAI;cAACZ,IAAI,EAAC,UAAU;cAACa,KAAK,EAAC,UAAU;cAACC,YAAY,EAAC,EAAE;cAACI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtD,OAAO,EAAE;cAA8B,CAAC,CAAE;cAAAyC,QAAA,eAC9HhC,OAAA;gBACEyC,IAAI,EAAC,UAAU;gBACfV,SAAS,EAAC,oKAAoK;gBAC9KW,WAAW,EAAC;cAA0B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZpC,OAAA;cAAK+B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhC,OAAA;gBACEyC,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,oNAAoN;gBAC9NkB,QAAQ,EAAEtC,OAAQ;gBAAAqB,QAAA,EAEjBrB,OAAO,GAAG,qBAAqB,GAAG;cAAgB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eAETpC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BhC,OAAA,CAACL,IAAI;kBACHyD,EAAE,EAAC,QAAQ;kBACXrB,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,EACzF;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChC,EAAA,CAtOQD,QAAQ;EAAA,QAMEP,WAAW;AAAA;AAAAyD,EAAA,GANrBlD,QAAQ;AAwOjB,eAAeA,QAAQ;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}