{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ContentRenderer.js\";\nimport React from 'react';\nimport { InlineMath, BlockMath } from 'react-katex';\nimport 'katex/dist/katex.min.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContentRenderer = ({\n  text\n}) => {\n  // Handle undefined, null, or empty text\n  if (!text || typeof text !== 'string') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 16\n    }, this);\n  }\n  const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\n  const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\n  // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\n  const boldTextRegex = /\\*\\*.*?\\*\\*/g;\n  // console.log('Text: ', text);\n  let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\n  const lines = modifiedText.split('\\n');\n  // console.log('Lines with symbol: ', lines);\n  const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n  // console.log('Lines: ', restoredLines);\n\n  const inlineMathSymbol = \"~~INLINEMATH~~\";\n  const blockMathSymbol = \"~~BLOCKMATH~~\";\n  const boldSymbol = \"~~BOLD~~\";\n  let newModifiedText = text.replace(blockMathRegex, match => {\n    return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\n    return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(boldTextRegex, match => {\n    // console.log('Bold Part: ', match);\n    return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\n  });\n  const newLines = newModifiedText.split('\\n');\n  const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n\n  // console.log('New Modified Text: ', newModifiedText);\n\n  const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\n  const logger = () => {\n    newRestoredLines.map((line, lineIndex) => {\n      // console.log(`Line ${lineIndex + 1}: `, line);\n      line.split(newRegex).map((part, index) => {\n        // console.log(`Line ${lineIndex + 1}, Part ${index + 1}: `, part);\n      });\n    });\n  };\n  logger();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: newRestoredLines.map((line, lineIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: line.trim() === '' ? /*#__PURE__*/_jsxDEV(\"br\", {}, `br-${lineIndex}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 25\n      }, this) : line.split(newRegex).map((part, index) => {\n        if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\n              if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(InlineMath, {\n                  children: nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 53\n                }, this);\n              } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(BlockMath, {\n                  children: nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 53\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: nestedPart\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 57\n                  }, this)\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 53\n                }, this);\n              }\n            })\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(InlineMath, {\n            children: part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(BlockMath, {\n            children: part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 37\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              whiteSpace: 'pre-wrap'\n            },\n            children: part\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 37\n          }, this);\n        }\n      })\n    }, lineIndex, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 9\n  }, this);\n};\n_c = ContentRenderer;\nexport default ContentRenderer;\nvar _c;\n$RefreshReg$(_c, \"ContentRenderer\");", "map": {"version": 3, "names": ["React", "InlineMath", "BlockMath", "jsxDEV", "_jsxDEV", "Content<PERSON><PERSON><PERSON>", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "inlineMathRegex", "blockMathRegex", "boldTextRegex", "modifiedText", "replace", "match", "lines", "split", "restoredLines", "map", "line", "inlineMathSymbol", "blockMathSymbol", "boldSymbol", "newModifiedText", "newLines", "newRestoredLines", "newRegex", "logger", "lineIndex", "part", "index", "children", "trim", "startsWith", "endsWith", "Fragment", "nested<PERSON><PERSON>", "n_index", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ContentRenderer.js"], "sourcesContent": ["import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    const logger = () => {\r\n        newRestoredLines.map((line, lineIndex) => {\r\n            // console.log(`Line ${lineIndex + 1}: `, line);\r\n            line.split(newRegex).map((part, index) => {\r\n                // console.log(`Line ${lineIndex + 1}, Part ${index + 1}: `, part);\r\n            })\r\n        });\r\n    };\r\n\r\n    logger();\r\n\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,SAAS,QAAQ,aAAa;AACnD,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAClC;EACA,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnC,oBAAOF,OAAA;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC;EACtB;EAEA,MAAMC,eAAe,GAAG,cAAc;EACtC,MAAMC,cAAc,GAAG,eAAe;EACtC;EACA,MAAMC,aAAa,GAAG,cAAc;EACpC;EACA,IAAIC,YAAY,GAAGR,IAAI,CAACS,OAAO,CAACH,cAAc,EAAEI,KAAK,IAAIA,KAAK,CAACD,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;EAC7F,MAAME,KAAK,GAAGH,YAAY,CAACI,KAAK,CAAC,IAAI,CAAC;EACtC;EACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACN,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;EAC7E;;EAKA,MAAMO,gBAAgB,GAAG,gBAAgB;EACzC,MAAMC,eAAe,GAAG,eAAe;EACvC,MAAMC,UAAU,GAAG,UAAU;EAE7B,IAAIC,eAAe,GAAGnB,IAAI,CAACS,OAAO,CAACH,cAAc,EAAEI,KAAK,IAAI;IACxD,OAAQ,gBAAeA,KAAK,CAACD,OAAO,CAAC,KAAK,EAAE,aAAa,CAAE,eAAc;EAC7E,CAAC,CAAC;EAEFU,eAAe,GAAGA,eAAe,CAACV,OAAO,CAACJ,eAAe,EAAEK,KAAK,IAAI;IAChE,OAAQ,iBAAgBA,KAAM,gBAAe;EACjD,CAAC,CAAC;EAEFS,eAAe,GAAGA,eAAe,CAACV,OAAO,CAACF,aAAa,EAAEG,KAAK,IAAI;IAC9D;IACA,OAAQ,WAAUA,KAAK,CAACD,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,UAAS;EAC1D,CAAC,CAAC;EAEF,MAAMW,QAAQ,GAAGD,eAAe,CAACP,KAAK,CAAC,IAAI,CAAC;EAE5C,MAAMS,gBAAgB,GAAGD,QAAQ,CAACN,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACN,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;;EAEnF;;EAEA,MAAMa,QAAQ,GAAG,yGAAyG;EAE1H,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACjBF,gBAAgB,CAACP,GAAG,CAAC,CAACC,IAAI,EAAES,SAAS,KAAK;MACtC;MACAT,IAAI,CAACH,KAAK,CAACU,QAAQ,CAAC,CAACR,GAAG,CAAC,CAACW,IAAI,EAAEC,KAAK,KAAK;QACtC;MAAA,CACH,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EAEDH,MAAM,CAAC,CAAC;EAER,oBACIzB,OAAA;IAAA6B,QAAA,EACKN,gBAAgB,CAACP,GAAG,CAAC,CAACC,IAAI,EAAES,SAAS,kBAClC1B,OAAA;MAAA6B,QAAA,EACKZ,IAAI,CAACa,IAAI,CAAC,CAAC,KAAK,EAAE,gBACf9B,OAAA,WAAU,MAAK0B,SAAU,EAAC;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GAE9BW,IAAI,CAACH,KAAK,CAACU,QAAQ,CAAC,CAACR,GAAG,CAAC,CAACW,IAAI,EAAEC,KAAK,KAAK;QACtC,IAAID,IAAI,CAACI,UAAU,CAACX,UAAU,CAAC,IAAIO,IAAI,CAACK,QAAQ,CAACZ,UAAU,CAAC,EAAE;UAC1D,oBACIpB,OAAA,CAACJ,KAAK,CAACqC,QAAQ;YAAAJ,QAAA,EACVF,IAAI,CAAChB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACG,KAAK,CAACU,QAAQ,CAAC,CAACR,GAAG,CAAC,CAACkB,UAAU,EAAEC,OAAO,KAAK;cACxE,IAAID,UAAU,CAACH,UAAU,CAACb,gBAAgB,CAAC,IAAIgB,UAAU,CAACF,QAAQ,CAACd,gBAAgB,CAAC,EAAE;gBAClF,oBACIlB,OAAA,CAACH,UAAU;kBAAAgC,QAAA,EACNK,UAAU,CAACvB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;gBAAC,GADxD,GAAEe,SAAU,IAAGE,KAAM,IAAGO,OAAQ,EAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAErB,CAAC,MAAM,IAAI4B,UAAU,CAACH,UAAU,CAACZ,eAAe,CAAC,IAAIe,UAAU,CAACF,QAAQ,CAACb,eAAe,CAAC,EAAE;gBACvF,oBACInB,OAAA,CAACF,SAAS;kBAAA+B,QAAA,EACLK,UAAU,CAACvB,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;gBAAC,GADrD,GAAEe,SAAU,IAAGE,KAAM,IAAGO,OAAQ,EAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEpB,CAAC,MAAM;gBACH,oBACIN,OAAA;kBAA+CoC,KAAK,EAAE;oBAAEC,UAAU,EAAE;kBAAW,CAAE;kBAAAR,QAAA,eAC7E7B,OAAA;oBAAA6B,QAAA,EAASK;kBAAU;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC,GADrB,GAAEoB,SAAU,IAAGE,KAAM,IAAGO,OAAQ,EAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEf;YACJ,CAAC;UAAC,GArBgB,GAAEoB,SAAU,IAAGE,KAAM,EAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsB5B,CAAC;QAEzB,CAAC,MAAM,IAAIqB,IAAI,CAACI,UAAU,CAACb,gBAAgB,CAAC,IAAIS,IAAI,CAACK,QAAQ,CAACd,gBAAgB,CAAC,EAAE;UAC7E,oBACIlB,OAAA,CAACH,UAAU;YAAAgC,QAAA,EACNF,IAAI,CAAChB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;UAAC,GADlD,GAAEe,SAAU,IAAGE,KAAM,EAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAErB,CAAC,MAAM,IAAIqB,IAAI,CAACI,UAAU,CAACZ,eAAe,CAAC,IAAIQ,IAAI,CAACK,QAAQ,CAACb,eAAe,CAAC,EAAE;UAC3E,oBACInB,OAAA,CAACF,SAAS;YAAA+B,QAAA,EACLF,IAAI,CAAChB,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;UAAC,GAD/C,GAAEe,SAAU,IAAGE,KAAM,EAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEpB,CAAC,MAAM;UACH,oBACIN,OAAA;YAAoCoC,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAW,CAAE;YAAAR,QAAA,EACjEF;UAAI,GADG,GAAED,SAAU,IAAGE,KAAM,EAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEf;MACJ,CAAC;IAAC,GAlDAoB,SAAS;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmDd,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAGd,CAAC;AAACgC,EAAA,GAnHIrC,eAAe;AAqHrB,eAAeA,eAAe;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}