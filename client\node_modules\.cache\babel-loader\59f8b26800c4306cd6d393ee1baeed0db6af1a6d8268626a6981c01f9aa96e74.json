{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Login\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message } from \"antd\";\nimport React from \"react\";\nimport './index.css';\nimport Logo from '../../../assets/logo.png';\nimport { useDispatch } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { loginUser } from \"../../../apicalls/users\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n      const response = await loginUser(values);\n      dispatch(HideLoading());\n      console.log('Login response:', response); // Debug log\n\n      if (response.success) {\n        message.success(response.message);\n        localStorage.setItem(\"token\", response.data);\n\n        // Check if user is admin from the response\n        if (response.response && response.response.isAdmin) {\n          navigate(\"/admin/users\");\n        } else {\n          navigate(\"/user/quiz\");\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Login error:', error); // Debug log\n      message.error(error.message);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-center items-center h-screen w-screen bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card p-3 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"brainwave-logo\",\n            className: \"login-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          className: \"mt-2\",\n          onFinish: onFinish,\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"primary-contained-btn mt-2 w-100\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"underline\",\n              children: \"Not a member? Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"ZaVe+Vo7W9FMoQ/aTgBrV7UvA04=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["Form", "message", "React", "Logo", "useDispatch", "Link", "useNavigate", "loginUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "dispatch", "onFinish", "values", "response", "console", "log", "success", "localStorage", "setItem", "data", "isAdmin", "error", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "name", "label", "initialValue", "type", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Login/index.js"], "sourcesContent": ["import { Form, message } from \"antd\";\r\nimport React from \"react\";\r\nimport './index.css';\r\nimport Logo from '../../../assets/logo.png';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { loginUser } from \"../../../apicalls/users\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Login() {\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch();\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await loginUser(values);\r\n      dispatch(HideLoading());\r\n\r\n      console.log('Login response:', response); // Debug log\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        localStorage.setItem(\"token\", response.data);\r\n\r\n        // Check if user is admin from the response\r\n        if (response.response && response.response.isAdmin) {\r\n          navigate(\"/admin/users\");\r\n        } else {\r\n          navigate(\"/user/quiz\");\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Login error:', error); // Debug log\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-center items-center h-screen w-screen bg-primary\">\r\n      <div className=\"card p-3 bg-white\">\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"flex justify-center\">\r\n            <img src={Logo} alt=\"brainwave-logo\" className=\"login-logo\"/>\r\n          </div>\r\n          <div className=\"divider\"></div>\r\n          <Form layout=\"vertical\" className=\"mt-2\" onFinish={onFinish}>\r\n            <Form.Item name=\"email\" label=\"Email\" initialValue=\"\">\r\n              <input type=\"text\" />\r\n            </Form.Item>\r\n            <Form.Item name=\"password\" label=\"Password\" initialValue=\"\">\r\n              <input type=\"password\" />\r\n            </Form.Item>\r\n\r\n            <div className=\"flex flex-col gap-2\">\r\n              <button\r\n                type=\"submit\"\r\n                className=\"primary-contained-btn mt-2 w-100\"\r\n              >\r\n                Login\r\n              </button>\r\n              <Link to=\"/register\" className=\"underline\">\r\n                Not a member? Register\r\n              </Link>\r\n            </div>\r\n          </Form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AACpB,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACFF,QAAQ,CAACN,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMS,QAAQ,GAAG,MAAMX,SAAS,CAACU,MAAM,CAAC;MACxCF,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MAEvBW,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC,CAAC,CAAC;;MAE1C,IAAIA,QAAQ,CAACG,OAAO,EAAE;QACpBpB,OAAO,CAACoB,OAAO,CAACH,QAAQ,CAACjB,OAAO,CAAC;QACjCqB,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACM,IAAI,CAAC;;QAE5C;QACA,IAAIN,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,CAACO,OAAO,EAAE;UAClDX,QAAQ,CAAC,cAAc,CAAC;QAC1B,CAAC,MAAM;UACLA,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,MAAM;QACLb,OAAO,CAACyB,KAAK,CAACR,QAAQ,CAACjB,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdX,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MACvBW,OAAO,CAACO,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC,CAAC,CAAC;MACtCzB,OAAO,CAACyB,KAAK,CAACA,KAAK,CAACzB,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,oBACEU,OAAA;IAAKgB,SAAS,EAAC,+DAA+D;IAAAC,QAAA,eAC5EjB,OAAA;MAAKgB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCjB,OAAA;QAAKgB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjB,OAAA;UAAKgB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCjB,OAAA;YAAKkB,GAAG,EAAE1B,IAAK;YAAC2B,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNvB,OAAA;UAAKgB,SAAS,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/BvB,OAAA,CAACX,IAAI;UAACmC,MAAM,EAAC,UAAU;UAACR,SAAS,EAAC,MAAM;UAACX,QAAQ,EAAEA,QAAS;UAAAY,QAAA,gBAC1DjB,OAAA,CAACX,IAAI,CAACoC,IAAI;YAACC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC,OAAO;YAACC,YAAY,EAAC,EAAE;YAAAX,QAAA,eACnDjB,OAAA;cAAO6B,IAAI,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACZvB,OAAA,CAACX,IAAI,CAACoC,IAAI;YAACC,IAAI,EAAC,UAAU;YAACC,KAAK,EAAC,UAAU;YAACC,YAAY,EAAC,EAAE;YAAAX,QAAA,eACzDjB,OAAA;cAAO6B,IAAI,EAAC;YAAU;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEZvB,OAAA;YAAKgB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCjB,OAAA;cACE6B,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC7C;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA,CAACN,IAAI;cAACoC,EAAE,EAAC,WAAW;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrB,EAAA,CA/DQD,KAAK;EAAA,QACKN,WAAW,EACXF,WAAW;AAAA;AAAAsC,EAAA,GAFrB9B,KAAK;AAiEd,eAAeA,KAAK;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}