{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport Flag from \"../assets/tanzania-flag.png\";\nimport Logo2 from \"../assets/logo-2.png\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport { useTheme } from \"../contexts/ThemeContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [menu, setMenu] = useState([]);\n  const [collapsed, setCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const activeRoute = location.pathname;\n  const userMenu = [{\n    title: \"Hub\",\n    paths: [\"/user/hub\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-apps-2-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/hub\")\n  }, {\n    title: \"Quiz\",\n    paths: [\"/user/quiz\", \"/user/write-exam\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-pencil-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/quiz\")\n  }, {\n    title: \"Reports\",\n    paths: [\"/user/reports\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-bar-chart-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/reports\")\n  }, {\n    title: \"Ranking\",\n    paths: [\"/user/ranking\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-trophy-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/ranking\")\n  }, {\n    title: \"Study Material\",\n    paths: [\"/user/study-material\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-book-open-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/study-material\")\n  }, {\n    title: \"About Us\",\n    paths: [\"/user/about-us\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-information-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/about-us\")\n  }, {\n    title: \"Ask AI\",\n    paths: [\"/user/chat\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-chat-smile-2-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/chat\")\n  }, {\n    title: \"Plans\",\n    paths: [\"/user/plans\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-calendar-check-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/user/plans\")\n  }, {\n    title: \"Forum\",\n    paths: [\"/forum\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-discuss-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/forum\")\n  }, {\n    title: \"Profile\",\n    paths: [\"/profile\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-user-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/profile\")\n  }, {\n    title: \"Logout\",\n    paths: [\"/logout\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-logout-box-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this),\n    onClick: () => {\n      localStorage.removeItem(\"token\");\n      navigate(\"/login\");\n    }\n  }];\n  const adminMenu = [{\n    title: \"Users\",\n    paths: [\"/admin/users\", \"/admin/users/add\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-file-list-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/users\")\n  }, {\n    title: \"Exams\",\n    paths: [\"/admin/exams\", \"/admin/exams/add\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-file-list-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/exams\")\n  }, {\n    title: \"AI Questions\",\n    paths: [\"/admin/ai-questions\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-robot-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/ai-questions\")\n  }, {\n    title: \"Study Materials\",\n    paths: [\"/admin/study-materials\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-book-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/study-materials\")\n  }, {\n    title: \"Reports\",\n    paths: [\"/admin/reports\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-bar-chart-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/reports\")\n  }, {\n    title: \"Forum\",\n    paths: [\"/forum\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-discuss-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/forum\")\n  }, {\n    title: \"Profile\",\n    paths: [\"/profile\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-user-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/profile\")\n  }, {\n    title: \"Announcements\",\n    paths: [\"/admin/announcements\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-notification-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate(\"/admin/announcements\")\n  }, {\n    title: \"Logout\",\n    paths: [\"/logout\"],\n    icon: /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"ri-logout-box-line\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 13\n    }, this),\n    onClick: () => {\n      localStorage.removeItem(\"token\");\n      navigate(\"/login\");\n    }\n  }];\n  const getUserData = async () => {\n    try {\n      console.log('Getting user data...'); // Debug log\n      const response = await getUserInfo();\n      console.log('User data response:', response); // Debug log\n\n      if (response.success) {\n        dispatch(SetUser(response.data));\n        if (response.data.isAdmin) {\n          setMenu(adminMenu);\n        } else {\n          setMenu(userMenu);\n        }\n      } else {\n        console.error('Failed to get user data:', response.message); // Debug log\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      console.error('Error getting user data:', error); // Debug log\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    // Function to handle resizing\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n      setCollapsed(window.innerWidth < 768);\n    };\n\n    // Add resize event listener\n    window.addEventListener(\"resize\", handleResize);\n    if (window.innerWidth < 768) {\n      setIsMobile(true);\n      setCollapsed(true);\n    }\n\n    // Check for token and navigate\n    const token = localStorage.getItem(\"token\");\n    console.log('Token check:', token ? 'Token exists' : 'No token found'); // Debug log\n\n    if (token) {\n      getUserData();\n    } else {\n      console.log('No token, redirecting to login'); // Debug log\n      navigate(\"/login\");\n    }\n\n    // Cleanup the event listener when the component is unmounted\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n  const getIsActiveOrNot = paths => {\n    if (paths.includes(activeRoute)) {\n      return true;\n    } else {\n      if (activeRoute.includes(\"/admin/exams/edit\") && paths.includes(\"/admin/exams\")) {\n        return true;\n      }\n      if (activeRoute.includes(\"/user/write-exam\") && paths.includes(\"/user/write-exam\")) {\n        return true;\n      }\n    }\n    return false;\n  };\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: \"nav-modern bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-30 shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCollapsed(false),\n                className: \"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"ri-menu-line text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), !isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCollapsed(!collapsed),\n                className: \"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `ri-${collapsed ? 'menu' : 'close'}-line text-xl`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), isMobile && collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Logo2,\n                  alt: \"BrainWave Logo\",\n                  className: \"w-10 h-10 rounded-lg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-gray-900 text-lg\",\n                  children: [\"BRAIN\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-primary-600\",\n                    children: \"WAVE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 28\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), !isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Logo2,\n                  alt: \"BrainWave Logo\",\n                  className: \"w-10 h-10 rounded-lg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-gray-900 text-lg\",\n                  children: [\"BRAIN\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-primary-600\",\n                    children: \"WAVE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 28\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: toggleTheme,\n                className: \"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `ri-${isDarkMode ? 'sun' : 'moon'}-line text-xl`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden sm:block text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-semibold text-gray-900\",\n                    children: user === null || user === void 0 ? void 0 : user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600 font-medium\",\n                    children: user !== null && user !== void 0 && user.isAdmin ? \"Administrator\" : `Class ${user === null || user === void 0 ? void 0 : user.class} Student`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center border-2 border-primary-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-primary-700 font-bold text-sm\",\n                    children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Flag,\n                  alt: \"Tanzania Flag\",\n                  className: \"w-8 h-6 rounded border border-gray-200 shadow-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 309,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"veOd34B4XSYB9LAo/qSmo0RjgbE=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation, useTheme];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "AnimatePresence", "Flag", "Logo2", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "useTheme", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "_user$name", "_user$name$charAt", "user", "state", "menu", "setMenu", "collapsed", "setCollapsed", "isMobile", "setIsMobile", "isPaymentPending", "setIsPaymentPending", "intervalRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "activeRoute", "pathname", "userMenu", "title", "paths", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "localStorage", "removeItem", "adminMenu", "getUserData", "console", "log", "response", "success", "data", "isAdmin", "error", "handleResize", "window", "innerWidth", "addEventListener", "token", "getItem", "removeEventListener", "getIsActiveOrNot", "includes", "verifyPaymentStatus", "paymentStatus", "current", "clearInterval", "paymentRequired", "setInterval", "getButtonClass", "isDarkMode", "toggleTheme", "header", "initial", "y", "opacity", "animate", "src", "alt", "name", "class", "char<PERSON>t", "toUpperCase", "div", "transition", "duration", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport Flag from \"../assets/tanzania-flag.png\";\r\nimport Logo2 from \"../assets/logo-2.png\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport { useTheme } from \"../contexts/ThemeContext\";\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [menu, setMenu] = useState([]);\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const activeRoute = location.pathname;\r\n\r\n  const userMenu = [\r\n    {\r\n      title: \"Hub\",\r\n      paths: [\"/user/hub\"],\r\n      icon: <i className=\"ri-apps-2-line\"></i>,\r\n      onClick: () => navigate(\"/user/hub\"),\r\n    },\r\n    {\r\n      title: \"Quiz\",\r\n      paths: [\"/user/quiz\", \"/user/write-exam\"],\r\n      icon: <i className=\"ri-pencil-line\"></i>,\r\n      onClick: () => navigate(\"/user/quiz\"),\r\n    },\r\n\r\n    {\r\n      title: \"Reports\",\r\n      paths: [\"/user/reports\"],\r\n      icon: <i className=\"ri-bar-chart-line\"></i>,\r\n      onClick: () => navigate(\"/user/reports\"),\r\n    },\r\n    {\r\n      title: \"Ranking\",\r\n      paths: [\"/user/ranking\"],\r\n      icon: <i className=\"ri-trophy-line\"></i>,\r\n      onClick: () => navigate(\"/user/ranking\"),\r\n    },\r\n    {\r\n      title: \"Study Material\",\r\n      paths: [\"/user/study-material\"],\r\n      icon: <i className=\"ri-book-open-line\"></i>,\r\n      onClick: () => navigate(\"/user/study-material\"),\r\n    },\r\n    {\r\n      title: \"About Us\",\r\n      paths: [\"/user/about-us\"],\r\n      icon: <i className=\"ri-information-line\"></i>,\r\n      onClick: () => navigate(\"/user/about-us\"),\r\n    },\r\n    {\r\n      title: \"Ask AI\",\r\n      paths: [\"/user/chat\"],\r\n      icon: <i className=\"ri-chat-smile-2-line\"></i>,\r\n      onClick: () => navigate(\"/user/chat\"),\r\n    },\r\n    {\r\n      title: \"Plans\",\r\n      paths: [\"/user/plans\"],\r\n      icon: <i className=\"ri-calendar-check-line\"></i>,\r\n      onClick: () => navigate(\"/user/plans\"),\r\n    },\r\n    {\r\n      title: \"Forum\",\r\n      paths: [\"/forum\"],\r\n      icon: <i className=\"ri-discuss-line\"></i>,\r\n      onClick: () => navigate(\"/forum\"),\r\n    },\r\n    {\r\n      title: \"Profile\",\r\n      paths: [\"/profile\"],\r\n      icon: <i className=\"ri-user-line\"></i>,\r\n      onClick: () => navigate(\"/profile\"),\r\n    },\r\n    {\r\n      title: \"Logout\",\r\n      paths: [\"/logout\"],\r\n      icon: <i className=\"ri-logout-box-line\"></i>,\r\n      onClick: () => {\r\n        localStorage.removeItem(\"token\");\r\n        navigate(\"/login\");\r\n      },\r\n    },\r\n  ];\r\n\r\n  const adminMenu = [\r\n    {\r\n      title: \"Users\",\r\n      paths: [\"/admin/users\", \"/admin/users/add\"],\r\n      icon: <i className=\"ri-file-list-line\"></i>,\r\n      onClick: () => navigate(\"/admin/users\"),\r\n    },\r\n    {\r\n      title: \"Exams\",\r\n      paths: [\"/admin/exams\", \"/admin/exams/add\"],\r\n      icon: <i className=\"ri-file-list-line\"></i>,\r\n      onClick: () => navigate(\"/admin/exams\"),\r\n    },\r\n    {\r\n      title: \"AI Questions\",\r\n      paths: [\"/admin/ai-questions\"],\r\n      icon: <i className=\"ri-robot-line\"></i>,\r\n      onClick: () => navigate(\"/admin/ai-questions\"),\r\n    },\r\n    {\r\n      title: \"Study Materials\",\r\n      paths: [\"/admin/study-materials\"],\r\n      icon: <i className=\"ri-book-line\"></i>,\r\n      onClick: () => navigate(\"/admin/study-materials\"),\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      paths: [\"/admin/reports\"],\r\n      icon: <i className=\"ri-bar-chart-line\"></i>,\r\n      onClick: () => navigate(\"/admin/reports\"),\r\n    },\r\n    {\r\n      title: \"Forum\",\r\n      paths: [\"/forum\"],\r\n      icon: <i className=\"ri-discuss-line\"></i>,\r\n      onClick: () => navigate(\"/forum\"),\r\n    },\r\n    {\r\n      title: \"Profile\",\r\n      paths: [\"/profile\"],\r\n      icon: <i className=\"ri-user-line\"></i>,\r\n      onClick: () => navigate(\"/profile\"),\r\n    },\r\n    {\r\n      title: \"Announcements\",\r\n      paths: [\"/admin/announcements\"],\r\n      icon: <i className=\"ri-notification-line\"></i>,\r\n      onClick: () => navigate(\"/admin/announcements\"),\r\n    },\r\n    {\r\n      title: \"Logout\",\r\n      paths: [\"/logout\"],\r\n      icon: <i className=\"ri-logout-box-line\"></i>,\r\n      onClick: () => {\r\n        localStorage.removeItem(\"token\");\r\n        navigate(\"/login\");\r\n      },\r\n    },\r\n  ];\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      console.log('Getting user data...'); // Debug log\r\n      const response = await getUserInfo();\r\n      console.log('User data response:', response); // Debug log\r\n\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n        if (response.data.isAdmin) {\r\n          setMenu(adminMenu);\r\n        } else {\r\n          setMenu(userMenu);\r\n        }\r\n      } else {\r\n        console.error('Failed to get user data:', response.message); // Debug log\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error getting user data:', error); // Debug log\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Function to handle resizing\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n      setCollapsed(window.innerWidth < 768);\r\n    };\r\n\r\n    // Add resize event listener\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n      setCollapsed(true);\r\n    }\r\n\r\n    // Check for token and navigate\r\n    const token = localStorage.getItem(\"token\");\r\n    console.log('Token check:', token ? 'Token exists' : 'No token found'); // Debug log\r\n\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      console.log('No token, redirecting to login'); // Debug log\r\n      navigate(\"/login\");\r\n    }\r\n\r\n    // Cleanup the event listener when the component is unmounted\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const getIsActiveOrNot = (paths) => {\r\n    if (paths.includes(activeRoute)) {\r\n      return true;\r\n    } else {\r\n      if (\r\n        activeRoute.includes(\"/admin/exams/edit\") &&\r\n        paths.includes(\"/admin/exams\")\r\n      ) {\r\n        return true;\r\n      }\r\n      if (\r\n        activeRoute.includes(\"/user/write-exam\") &&\r\n        paths.includes(\"/user/write-exam\")\r\n      ) {\r\n        return true;\r\n      }\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* No sidebar - users will use hub for navigation */}\r\n\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Header */}\r\n        <motion.header\r\n          initial={{ y: -20, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"nav-modern bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-30 shadow-sm\"\r\n        >\r\n          <div className=\"px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"flex items-center justify-between h-16\">\r\n              {/* Left Section */}\r\n              <div className=\"flex items-center space-x-4\">\r\n                {/* Mobile Menu Button */}\r\n                {isMobile && (\r\n                  <button\r\n                    onClick={() => setCollapsed(false)}\r\n                    className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n                  >\r\n                    <i className=\"ri-menu-line text-xl\"></i>\r\n                  </button>\r\n                )}\r\n\r\n                {/* Desktop Collapse Button */}\r\n                {!isMobile && (\r\n                  <button\r\n                    onClick={() => setCollapsed(!collapsed)}\r\n                    className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n                  >\r\n                    <i className={`ri-${collapsed ? 'menu' : 'close'}-line text-xl`}></i>\r\n                  </button>\r\n                )}\r\n\r\n                {/* Logo for mobile when sidebar is collapsed */}\r\n                {isMobile && collapsed && (\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <img src={Logo2} alt=\"BrainWave Logo\" className=\"w-10 h-10 rounded-lg\" />\r\n                    <span className=\"font-bold text-gray-900 text-lg\">\r\n                      BRAIN<span className=\"text-primary-600\">WAVE</span>\r\n                    </span>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Always show logo and title on quiz pages */}\r\n                {!isMobile && (\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <img src={Logo2} alt=\"BrainWave Logo\" className=\"w-10 h-10 rounded-lg\" />\r\n                    <span className=\"font-bold text-gray-900 text-lg\">\r\n                      BRAIN<span className=\"text-primary-600\">WAVE</span>\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Right Section */}\r\n              <div className=\"flex items-center space-x-4\">\r\n                {/* Dark Mode Toggle */}\r\n                <button\r\n                  onClick={toggleTheme}\r\n                  className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n                >\r\n                  <i className={`ri-${isDarkMode ? 'sun' : 'moon'}-line text-xl`}></i>\r\n                </button>\r\n\r\n                {/* User Profile */}\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-sm font-semibold text-gray-900\">{user?.name}</div>\r\n                    <div className=\"text-xs text-gray-600 font-medium\">\r\n                      {user?.isAdmin ? \"Administrator\" : `Class ${user?.class} Student`}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center border-2 border-primary-300\">\r\n                    <span className=\"text-primary-700 font-bold text-sm\">\r\n                      {user?.name?.charAt(0)?.toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                  <img src={Flag} alt=\"Tanzania Flag\" className=\"w-8 h-6 rounded border border-gray-200 shadow-sm\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content */}\r\n        <main className=\"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMmC,WAAW,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM;IAAEmC;EAAiB,CAAC,GAAG5B,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACW,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAG9B,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACa,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAMkC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,WAAW,GAAGD,QAAQ,CAACE,QAAQ;EAErC,MAAMC,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,CAAC,WAAW,CAAC;IACpBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACxCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,WAAW;EACrC,CAAC,EACD;IACEK,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,CAAC,YAAY,EAAE,kBAAkB,CAAC;IACzCC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACxCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,YAAY;EACtC,CAAC,EAED;IACEK,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,eAAe,CAAC;IACxBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,eAAe;EACzC,CAAC,EACD;IACEK,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,eAAe,CAAC;IACxBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACxCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,eAAe;EACzC,CAAC,EACD;IACEK,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,CAAC,sBAAsB,CAAC;IAC/BC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,sBAAsB;EAChD,CAAC,EACD;IACEK,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAC,gBAAgB,CAAC;IACzBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC7CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,gBAAgB;EAC1C,CAAC,EACD;IACEK,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,CAAC,YAAY,CAAC;IACrBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC9CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,YAAY;EACtC,CAAC,EACD;IACEK,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,aAAa,CAAC;IACtBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAChDC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,aAAa;EACvC,CAAC,EACD;IACEK,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,QAAQ,CAAC;IACjBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACzCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,QAAQ;EAClC,CAAC,EACD;IACEK,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,UAAU,CAAC;IACnBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACtCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,UAAU;EACpC,CAAC,EACD;IACEK,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,CAAC,SAAS,CAAC;IAClBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC5CC,OAAO,EAAEA,CAAA,KAAM;MACbC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChCf,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,CACF;EAED,MAAMgB,SAAS,GAAG,CAChB;IACEX,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC3CC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,cAAc;EACxC,CAAC,EACD;IACEK,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAC3CC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,cAAc;EACxC,CAAC,EACD;IACEK,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,CAAC,qBAAqB,CAAC;IAC9BC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACvCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,qBAAqB;EAC/C,CAAC,EACD;IACEK,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,CAAC,wBAAwB,CAAC;IACjCC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACtCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,wBAAwB;EAClD,CAAC,EACD;IACEK,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,gBAAgB,CAAC;IACzBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC3CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,gBAAgB;EAC1C,CAAC,EACD;IACEK,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,QAAQ,CAAC;IACjBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACzCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,QAAQ;EAClC,CAAC,EACD;IACEK,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,UAAU,CAAC;IACnBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IACtCC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,UAAU;EACpC,CAAC,EACD;IACEK,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAC,sBAAsB,CAAC;IAC/BC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC9CC,OAAO,EAAEA,CAAA,KAAMb,QAAQ,CAAC,sBAAsB;EAChD,CAAC,EACD;IACEK,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,CAAC,SAAS,CAAC;IAClBC,IAAI,eAAE7B,OAAA;MAAG8B,SAAS,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;IAC5CC,OAAO,EAAEA,CAAA,KAAM;MACbC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChCf,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,CACF;EAED,MAAMiB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC;MACrC,MAAMC,QAAQ,GAAG,MAAMvD,WAAW,CAAC,CAAC;MACpCqD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,QAAQ,CAAC,CAAC,CAAC;;MAE9C,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBtB,QAAQ,CAAC/B,OAAO,CAACoD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAChC,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBpC,OAAO,CAAC6B,SAAS,CAAC;QACpB,CAAC,MAAM;UACL7B,OAAO,CAACiB,QAAQ,CAAC;QACnB;MACF,CAAC,MAAM;QACLc,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEJ,QAAQ,CAAChE,OAAO,CAAC,CAAC,CAAC;QAC7DA,OAAO,CAACoE,KAAK,CAACJ,QAAQ,CAAChE,OAAO,CAAC;QAC/B4C,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC,CAAC,CAAC;MAClDxB,QAAQ,CAAC,QAAQ,CAAC;MAClB5C,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd;IACA,MAAMmE,YAAY,GAAGA,CAAA,KAAM;MACzBlC,WAAW,CAACmC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;MACpCtC,YAAY,CAACqC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;IACvC,CAAC;;IAED;IACAD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAE/C,IAAIC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MAC3BpC,WAAW,CAAC,IAAI,CAAC;MACjBF,YAAY,CAAC,IAAI,CAAC;IACpB;;IAEA;IACA,MAAMwC,KAAK,GAAGf,YAAY,CAACgB,OAAO,CAAC,OAAO,CAAC;IAC3CZ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEU,KAAK,GAAG,cAAc,GAAG,gBAAgB,CAAC,CAAC,CAAC;;IAExE,IAAIA,KAAK,EAAE;MACTZ,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC;MAC/CnB,QAAQ,CAAC,QAAQ,CAAC;IACpB;;IAEA;IACA,OAAO,MAAM;MACX0B,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,gBAAgB,GAAI1B,KAAK,IAAK;IAClC,IAAIA,KAAK,CAAC2B,QAAQ,CAAC/B,WAAW,CAAC,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,IACEA,WAAW,CAAC+B,QAAQ,CAAC,mBAAmB,CAAC,IACzC3B,KAAK,CAAC2B,QAAQ,CAAC,cAAc,CAAC,EAC9B;QACA,OAAO,IAAI;MACb;MACA,IACE/B,WAAW,CAAC+B,QAAQ,CAAC,kBAAkB,CAAC,IACxC3B,KAAK,CAAC2B,QAAQ,CAAC,kBAAkB,CAAC,EAClC;QACA,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAED3E,SAAS,CAAC,MAAM;IACd,IAAIkC,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACyC,QAAQ,CAAC/B,WAAW,CAAC,EAAE;MACrEF,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEU,WAAW,EAAEF,QAAQ,CAAC,CAAC;EAE7C,MAAMkC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMZ,IAAI,GAAG,MAAMjD,kBAAkB,CAAC,CAAC;MACvC6C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEG,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEE,KAAK,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,aAAa,MAAK,MAAM,EAAE;QACjD,IAAIxC,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACzB,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACAmB,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,QAAQ,CAACzB,eAAe,CAACgD,IAAI,CAAC,CAAC;QAC/B,IAAI5B,WAAW,CAAC0C,OAAO,EAAE;UACvBC,aAAa,CAAC3C,WAAW,CAAC0C,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEK,KAAK,CAAC;MACpDzB,QAAQ,CAACzB,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/BmB,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACd,IAAI0B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsD,eAAe,IAAI,EAACtD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuC,OAAO,GAAE;MAC3CL,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAItB,yBAAyB,EAAE;QAC7BqB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CzB,WAAW,CAAC0C,OAAO,GAAGG,WAAW,CAAC,MAAM;UACtCrB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCe,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTnC,QAAQ,CAACxB,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACsB,yBAAyB,CAAC,CAAC;EAE/BvC,SAAS,CAAC,MAAM;IACd,IAAI0B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsD,eAAe,IAAI,EAACtD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuC,OAAO,GAAE;MAC3CL,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/Be,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAClD,IAAI,EAAEkB,WAAW,CAAC,CAAC;EAGvB,MAAMsC,cAAc,GAAInC,KAAK,IAAK;IAChC;IACA,IAAI,CAACrB,IAAI,CAACsD,eAAe,IAAIjC,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAAV,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwC,aAAa,MAAK,MAAM,IAAInD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsD,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAGD,MAAM;IAAEG,UAAU;IAAEC;EAAY,CAAC,GAAGlE,QAAQ,CAAC,CAAC;EAE9C,oBACEE,OAAA;IAAK8B,SAAS,EAAC,0CAA0C;IAAA5B,QAAA,eAKvDF,OAAA;MAAK8B,SAAS,EAAC,mCAAmC;MAAA5B,QAAA,gBAEhDF,OAAA,CAACjB,MAAM,CAACkF,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BtC,SAAS,EAAC,8FAA8F;QAAA5B,QAAA,eAExGF,OAAA;UAAK8B,SAAS,EAAC,sBAAsB;UAAA5B,QAAA,eACnCF,OAAA;YAAK8B,SAAS,EAAC,wCAAwC;YAAA5B,QAAA,gBAErDF,OAAA;cAAK8B,SAAS,EAAC,6BAA6B;cAAA5B,QAAA,GAEzCU,QAAQ,iBACPZ,OAAA;gBACEmC,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,KAAK,CAAE;gBACnCmB,SAAS,EAAC,2FAA2F;gBAAA5B,QAAA,eAErGF,OAAA;kBAAG8B,SAAS,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CACT,EAGA,CAACtB,QAAQ,iBACRZ,OAAA;gBACEmC,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,CAACD,SAAS,CAAE;gBACxCoB,SAAS,EAAC,2FAA2F;gBAAA5B,QAAA,eAErGF,OAAA;kBAAG8B,SAAS,EAAG,MAAKpB,SAAS,GAAG,MAAM,GAAG,OAAQ;gBAAe;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CACT,EAGAtB,QAAQ,IAAIF,SAAS,iBACpBV,OAAA;gBAAK8B,SAAS,EAAC,6BAA6B;gBAAA5B,QAAA,gBAC1CF,OAAA;kBAAKsE,GAAG,EAAEpF,KAAM;kBAACqF,GAAG,EAAC,gBAAgB;kBAACzC,SAAS,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzElC,OAAA;kBAAM8B,SAAS,EAAC,iCAAiC;kBAAA5B,QAAA,GAAC,OAC3C,eAAAF,OAAA;oBAAM8B,SAAS,EAAC,kBAAkB;oBAAA5B,QAAA,EAAC;kBAAI;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN,EAGA,CAACtB,QAAQ,iBACRZ,OAAA;gBAAK8B,SAAS,EAAC,6BAA6B;gBAAA5B,QAAA,gBAC1CF,OAAA;kBAAKsE,GAAG,EAAEpF,KAAM;kBAACqF,GAAG,EAAC,gBAAgB;kBAACzC,SAAS,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzElC,OAAA;kBAAM8B,SAAS,EAAC,iCAAiC;kBAAA5B,QAAA,GAAC,OAC3C,eAAAF,OAAA;oBAAM8B,SAAS,EAAC,kBAAkB;oBAAA5B,QAAA,EAAC;kBAAI;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNlC,OAAA;cAAK8B,SAAS,EAAC,6BAA6B;cAAA5B,QAAA,gBAE1CF,OAAA;gBACEmC,OAAO,EAAE6B,WAAY;gBACrBlC,SAAS,EAAC,2FAA2F;gBAAA5B,QAAA,eAErGF,OAAA;kBAAG8B,SAAS,EAAG,MAAKiC,UAAU,GAAG,KAAK,GAAG,MAAO;gBAAe;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eAGTlC,OAAA;gBAAK8B,SAAS,EAAC,6BAA6B;gBAAA5B,QAAA,gBAC1CF,OAAA;kBAAK8B,SAAS,EAAC,4BAA4B;kBAAA5B,QAAA,gBACzCF,OAAA;oBAAK8B,SAAS,EAAC,qCAAqC;oBAAA5B,QAAA,EAAEI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE;kBAAI;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvElC,OAAA;oBAAK8B,SAAS,EAAC,mCAAmC;oBAAA5B,QAAA,EAC/CI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuC,OAAO,GAAG,eAAe,GAAI,SAAQvC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,KAAM;kBAAS;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlC,OAAA;kBAAK8B,SAAS,EAAC,uIAAuI;kBAAA5B,QAAA,eACpJF,OAAA;oBAAM8B,SAAS,EAAC,oCAAoC;oBAAA5B,QAAA,EACjDI,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAEkE,IAAI,cAAApE,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYsE,MAAM,CAAC,CAAC,CAAC,cAAArE,iBAAA,uBAArBA,iBAAA,CAAuBsE,WAAW,CAAC;kBAAC;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNlC,OAAA;kBAAKsE,GAAG,EAAErF,IAAK;kBAACsF,GAAG,EAAC,eAAe;kBAACzC,SAAS,EAAC;gBAAkD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGhBlC,OAAA;QAAM8B,SAAS,EAAC,gEAAgE;QAAA5B,QAAA,eAC9EF,OAAA,CAACjB,MAAM,CAAC6F,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BU,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CjD,SAAS,EAAC,QAAQ;UAAA5B,QAAA,EAEjBA;QAAQ;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/B,EAAA,CAzYQF,cAAc;EAAA,QACJZ,WAAW,EAMCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW,EAsRQM,QAAQ;AAAA;AAAAkF,EAAA,GAjSrC/E,cAAc;AA2YvB,eAAeA,cAAc;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}