{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { TbBrain } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { QuizGrid } from '../../../components/modern';\nimport './responsive.css';\nimport './style.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  useEffect(() => {\n    const getExams = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getAllExams();\n        dispatch(HideLoading());\n        if (response.success) {\n          setExams(response.data);\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n      }\n    };\n    getExams();\n  }, [dispatch]);\n  const handleQuizStart = quiz => {\n    navigate(`/quiz/${quiz._id}/instructions`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-listing-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-listing-content\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"quiz-listing-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"heading-2 text-gradient mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"inline w-10 h-10 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), \"Challenge Your Brain, Beat the Rest\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: exams.length > 0 ? /*#__PURE__*/_jsxDEV(QuizGrid, {\n          quizzes: exams,\n          onQuizStart: handleQuizStart,\n          className: \"quiz-grid-container\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-500 text-lg\",\n            children: \"No quizzes available at the moment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-400 text-sm mt-2\",\n            children: \"Check back later for new challenges!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Quiz, \"uDos1H95TrBTRx+IYLum2Qec+c4=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Quiz;\nexport default Quiz;\nvar _c;\n$RefreshReg$(_c, \"Quiz\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useDispatch", "useSelector", "motion", "message", "TbBrain", "getAllExams", "HideLoading", "ShowLoading", "QuizGrid", "jsxDEV", "_jsxDEV", "Quiz", "_s", "exams", "setExams", "navigate", "dispatch", "getExams", "response", "success", "data", "error", "handleQuizStart", "quiz", "_id", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "length", "quizzes", "onQuizStart", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { motion } from 'framer-motion';\r\nimport { message } from 'antd';\r\nimport { TbBrain } from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport { QuizGrid } from '../../../components/modern';\r\nimport './responsive.css';\r\nimport './style.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n\r\n  useEffect(() => {\r\n    const getExams = async () => {\r\n      try {\r\n        dispatch(ShowLoading());\r\n        const response = await getAllExams();\r\n        dispatch(HideLoading());\r\n\r\n        if (response.success) {\r\n          setExams(response.data);\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message);\r\n      }\r\n    };\r\n\r\n    getExams();\r\n  }, [dispatch]);\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    navigate(`/quiz/${quiz._id}/instructions`);\r\n  };\r\n\r\n  return (\r\n    <div className=\"quiz-listing-container\">\r\n      <div className=\"quiz-listing-content\">\r\n        {/* Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"quiz-listing-header\"\r\n        >\r\n          <div className=\"text-center mb-6\">\r\n            <h1 className=\"heading-2 text-gradient mb-4\">\r\n              <TbBrain className=\"inline w-10 h-10 mr-3\" />\r\n              Challenge Your Brain, Beat the Rest\r\n            </h1>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Quiz Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n        >\r\n          {exams.length > 0 ? (\r\n            <QuizGrid\r\n              quizzes={exams}\r\n              onQuizStart={handleQuizStart}\r\n              className=\"quiz-grid-container\"\r\n            />\r\n          ) : (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"text-gray-500 text-lg\">\r\n                No quizzes available at the moment.\r\n              </div>\r\n              <div className=\"text-gray-400 text-sm mt-2\">\r\n                Check back later for new challenges!\r\n              </div>\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAO,kBAAkB;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMkB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,MAAMmB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFD,QAAQ,CAACT,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMW,QAAQ,GAAG,MAAMb,WAAW,CAAC,CAAC;QACpCW,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAIY,QAAQ,CAACC,OAAO,EAAE;UACpBL,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAAC;QACzB,CAAC,MAAM;UACLjB,OAAO,CAACkB,KAAK,CAACH,QAAQ,CAACf,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdL,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC;QACvBH,OAAO,CAACkB,KAAK,CAACA,KAAK,CAAClB,OAAO,CAAC;MAC9B;IACF,CAAC;IAEDc,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACD,QAAQ,CAAC,CAAC;EAEd,MAAMM,eAAe,GAAIC,IAAI,IAAK;IAChCR,QAAQ,CAAE,SAAQQ,IAAI,CAACC,GAAI,eAAc,CAAC;EAC5C,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrChB,OAAA;MAAKe,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnChB,OAAA,CAACR,MAAM,CAACyB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAE/BhB,OAAA;UAAKe,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BhB,OAAA;YAAIe,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC1ChB,OAAA,CAACN,OAAO;cAACqB,SAAS,EAAC;YAAuB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uCAE/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbzB,OAAA,CAACR,MAAM,CAACyB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,EAE1Bb,KAAK,CAACyB,MAAM,GAAG,CAAC,gBACf5B,OAAA,CAACF,QAAQ;UACP+B,OAAO,EAAE1B,KAAM;UACf2B,WAAW,EAAElB,eAAgB;UAC7BG,SAAS,EAAC;QAAqB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,gBAEFzB,OAAA;UAAKe,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChB,OAAA;YAAKe,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzB,OAAA;YAAKe,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE5C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAzEID,IAAI;EAAA,QAESZ,WAAW,EACXC,WAAW;AAAA;AAAAyC,EAAA,GAHxB9B,IAAI;AA2EV,eAAeA,IAAI;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}