import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { getExamById } from '../../../apicalls/exams';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { Card, Button, Loading } from '../../../components/modern';
import {
  TbClock,
  TbQuestionMark,
  TbTrophy,
  TbAlertTriangle,
  TbPlayerPlay,
  TbArrowLeft,
  TbBrain
} from 'react-icons/tb';
import './responsive.css';

const QuizStart = () => {
  const [examData, setExamData] = useState(null);
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());

        if (response.success) {
          setExamData(response.data);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');
    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  const handleStartQuiz = () => {
    navigate(`/quiz/${id}/play`);
  };

  if (!examData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
        <Loading fullScreen text="Loading quiz details..." />
      </div>
    );
  }

  return (
    <div className="quiz-start-container">
      <div className="quiz-start-card">
        <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="quiz-start-header">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6 backdrop-blur-sm">
            <TbBrain className="w-10 h-10 text-white" />
          </div>
          <h1 className="quiz-start-title">{examData.name}</h1>
          <p className="quiz-start-subtitle">Ready to challenge yourself? Let's test your knowledge!</p>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }} className="quiz-start-info">
          <div className="quiz-info-item">
            <TbQuestionMark className="quiz-info-icon" />
            <div className="quiz-info-value">{examData.questions?.length || 0}</div>
            <div className="quiz-info-label">Questions</div>
          </div>
          <div className="quiz-info-item">
            <TbClock className="quiz-info-icon" />
            <div className="quiz-info-value">{examData.duration || 30}</div>
            <div className="quiz-info-label">Minutes</div>
          </div>
          <div className="quiz-info-item">
            <TbTrophy className="quiz-info-icon" />
            <div className="quiz-info-value">{examData.passingPercentage || 70}%</div>
            <div className="quiz-info-label">Pass Mark</div>
          </div>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
          <Card className="p-6 mb-6">
            <h2 className="heading-3 mb-6 flex items-center">
              <TbAlertTriangle className="w-6 h-6 text-warning-600 mr-2" /> Instructions
            </h2>
            <div className="space-y-4 text-gray-700">
              {["Read each question carefully before answering", "You can navigate between questions using Previous/Next buttons", "Make sure to answer all questions before submitting", "Keep an eye on the timer - the quiz will auto-submit when time runs out"].map((instruction, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="flex items-start space-x-3"
                >
                  <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-primary-600 font-bold text-sm">{index + 1}</span>
                  </div>
                  <p className="leading-relaxed">{instruction}</p>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
          <Card className="p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-primary-600 font-bold text-lg">
                  {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                </span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Welcome, {user?.name || 'Student'}!</h3>
                <p className="text-gray-600">
                  Level: <span className="badge-primary">{user?.level || 'Primary'}</span> • Class: <span className="badge-primary">{user?.class || 'N/A'}</span>
                </p>
              </div>
            </div>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6 }} className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="secondary"
            size="lg"
            onClick={() => navigate('/user/quiz')}
            icon={<TbArrowLeft />}
            className="sm:w-auto w-full"
          >
            Back to Quizzes
          </Button>
          <Button
            variant="gradient"
            size="lg"
            onClick={handleStartQuiz}
            icon={<TbPlayerPlay />}
            iconPosition="right"
            className="sm:w-auto w-full"
          >
            Start Quiz
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default QuizStart;
