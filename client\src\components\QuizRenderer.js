import React, { useState, useEffect } from 'react';
import '../pages/user/Quiz/responsive.css';

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  username = "Student",
  onNext,
  onPrevious,
  examTitle = "Quiz",
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isTimeWarning = timeLeft <= 60;
  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  const renderMCQ = () => {
    if (!question.options) {
      return (
        <div className="quiz-question-container">
          <div className="text-center text-danger">No options available for this question.</div>
        </div>
      );
    }

    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

    return (
      <div className="quiz-options">
        {Object.entries(question.options).map(([key, value], index) => {
          const optionKey = String(key).trim();
          const optionValue = String(value).trim();
          const label = optionLabels[index] || optionKey;

          return (
            <button
              key={optionKey}
              onClick={() => handleAnswerSelect(optionKey)}
              className={`quiz-option ${currentAnswer === optionKey ? 'selected' : ''}`}
            >
              <span className="quiz-option-letter">{label}</span>
              <span className="quiz-option-text">{optionValue}</span>
            </button>
          );
        })}
      </div>
    );
  };

  const renderFillBlank = () => (
    <div className="quiz-question-container">
      <label className="form-label">Your Answer:</label>
      <input
        type="text"
        value={currentAnswer}
        onChange={(e) => handleAnswerSelect(e.target.value)}
        placeholder="Type your answer here..."
        className="quiz-fill-input"
      />

      {currentAnswer && (
        <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200 shadow-lg">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center shadow-lg">
              ✓
            </div>
            <p className="text-emerald-800 font-semibold">
              Answer: {currentAnswer}
            </p>
          </div>
        </div>
      )}
    </div>
  );

  const renderImageQuestion = () => (
    <div className="space-y-8">
      {question.imageUrl && (
        <div className="text-center">
          <div className="inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200">
            <img
              src={question.imageUrl}
              alt="Question diagram"
              className="max-w-full max-h-96 rounded-lg mx-auto"
            />
          </div>
        </div>
      )}

      {question.options ? renderMCQ() : renderFillBlank()}
    </div>
  );

  return (
    <div className="quiz-container">
      <div className="quiz-progress-bar">
        <div
          className="quiz-progress-fill"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      <div className="quiz-progress-container">
        <div className="quiz-header-content">
          <div className="quiz-title-section">
            <h1>Challenge your brain, Beat the rest</h1>
            <p className="quiz-subtitle">
              Class {username} • {examTitle}
            </p>
          </div>
          <div className={`quiz-timer ${isTimeWarning ? 'warning' : ''}`}>
            {formatTime(timeLeft)}
          </div>
        </div>

        <div className="quiz-question-counter">
          Question {questionIndex + 1} of {totalQuestions}
        </div>
      </div>

      <div className="quiz-content">
        <div className="quiz-question-container">
          <div className="quiz-question-number">
            Question {questionIndex + 1}
          </div>

          <div className="quiz-question-text">
            {question.name}
          </div>

          {question.image && (
            <div className="quiz-image-container">
              <img
                src={question.image}
                alt="Question"
                className="quiz-image"
              />
            </div>
          )}

          {question.answerType === "Options" && renderMCQ()}
          {(question.answerType === "Free Text" || question.answerType === "Fill in the Blank") && renderFillBlank()}
          {question.imageUrl && renderImageQuestion()}
        </div>
      </div>

      <div className="quiz-navigation">
        <button
          onClick={onPrevious}
          disabled={questionIndex === 0}
          className={`quiz-nav-btn ${questionIndex === 0 ? 'secondary' : 'secondary'}`}
        >
          Previous
        </button>

        <button
          onClick={onNext}
          disabled={!isAnswered}
          className={`quiz-nav-btn ${!isAnswered ? 'secondary' : 'primary'}`}
        >
          {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'}
        </button>
      </div>
    </div>
  );
};

export default QuizRenderer;
