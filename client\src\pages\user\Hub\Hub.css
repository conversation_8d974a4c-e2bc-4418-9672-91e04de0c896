/* ===== RESPONSIVE HUB PAGE ===== */

/* ===== BASE STYLES ===== */
.hub-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;
  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.hub-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(0, 86, 210, 0.1) 0%, transparent 50%);
  z-index: 0;
}

.hub-content {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
}

/* ===== HEADER SECTION ===== */
.hub-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.hub-welcome {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hub-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.hub-quote {
  font-size: 1.125rem;
  color: #374151;
  font-style: italic;
  padding: 1rem 2rem;
  background: rgba(0, 123, 255, 0.05);
  border-left: 4px solid #007BFF;
  border-radius: 0.5rem;
  margin: 0 auto;
  max-width: 600px;
}

/* ===== GRID LAYOUT ===== */
.hub-grid-container {
  margin-bottom: 3rem;
}

.hub-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* ===== CARD STYLES ===== */
.hub-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1.5rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.hub-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007BFF 0%, #0056D2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.hub-card:hover::before {
  transform: scaleX(1);
}

.hub-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  border-color: rgba(0, 123, 255, 0.3);
}

.hub-card-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.hub-card:hover .hub-card-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 20px 25px -5px rgba(0, 123, 255, 0.4);
}

.hub-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.hub-card-description {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* ===== ANIMATIONS ===== */

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Bounce animation */
@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

/* Stagger animation delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }

/* Float animation for icons */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Bounce animation for icons */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25%);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* ===== BOTTOM DECORATION ===== */
.hub-bottom-decoration {
  text-align: center;
  margin-top: 4rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 1.5rem;
  backdrop-filter: blur(10px);
}

.hub-bottom-decoration .decoration-content {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  color: #6b7280;
  font-size: 1.125rem;
  font-weight: 600;
}

.hub-bottom-decoration .decoration-icon {
  font-size: 1.5rem;
  color: #007BFF;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
  .hub-container {
    padding: 1rem 0.75rem;
  }

  .hub-header {
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 1rem;
  }

  .hub-welcome {
    font-size: 2rem;
    margin-bottom: 0.75rem;
  }

  .hub-subtitle {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .hub-quote {
    font-size: 0.9375rem;
    padding: 0.875rem 1rem;
    margin: 0;
  }

  .hub-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .hub-card {
    padding: 1.5rem;
    border-radius: 1rem;
  }

  .hub-card-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .hub-card-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .hub-card-description {
    font-size: 0.875rem;
  }

  .hub-bottom-decoration {
    margin-top: 2rem;
    padding: 1.5rem;
    border-radius: 1rem;
  }

  .hub-bottom-decoration .decoration-content {
    flex-direction: column;
    gap: 0.5rem;
    font-size: 1rem;
  }

  .hub-bottom-decoration .decoration-icon {
    font-size: 1.25rem;
  }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .hub-container {
    padding: 1.5rem 1rem;
  }

  .hub-header {
    padding: 1.75rem;
    margin-bottom: 2.5rem;
  }

  .hub-welcome {
    font-size: 2.5rem;
  }

  .hub-subtitle {
    font-size: 1.125rem;
  }

  .hub-quote {
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }

  .hub-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .hub-card {
    padding: 1.75rem;
  }

  .hub-card-icon {
    width: 70px;
    height: 70px;
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }

  .hub-card-title {
    font-size: 1.375rem;
  }

  .hub-card-description {
    font-size: 0.9375rem;
  }

  .hub-bottom-decoration {
    margin-top: 3rem;
    padding: 1.75rem;
  }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .hub-container {
    padding: 2rem 1.5rem;
  }

  .hub-header {
    padding: 2rem;
    margin-bottom: 3rem;
  }

  .hub-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.75rem;
  }

  .hub-card {
    padding: 2rem;
  }

  .hub-card-icon {
    width: 75px;
    height: 75px;
    font-size: 1.875rem;
  }
}

/* Large Desktop (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .hub-container {
    padding: 2.5rem 2rem;
  }

  .hub-header {
    padding: 2.5rem;
    margin-bottom: 3.5rem;
  }

  .hub-welcome {
    font-size: 3.5rem;
  }

  .hub-subtitle {
    font-size: 1.375rem;
  }

  .hub-quote {
    font-size: 1.1875rem;
    padding: 1.25rem 2.5rem;
  }

  .hub-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .hub-card {
    padding: 2.5rem;
  }

  .hub-card-icon {
    width: 85px;
    height: 85px;
    font-size: 2.125rem;
    margin-bottom: 1.75rem;
  }

  .hub-card-title {
    font-size: 1.625rem;
    margin-bottom: 1rem;
  }

  .hub-card-description {
    font-size: 1.0625rem;
  }

  .hub-bottom-decoration {
    margin-top: 4.5rem;
    padding: 2.5rem;
  }

  .hub-bottom-decoration .decoration-content {
    font-size: 1.25rem;
  }

  .hub-bottom-decoration .decoration-icon {
    font-size: 1.75rem;
  }
}

/* Ultra-Wide Screens (1441px+) */
@media (min-width: 1441px) {
  .hub-container {
    padding: 3rem 2.5rem;
  }

  .hub-content {
    max-width: 1600px;
  }

  .hub-header {
    padding: 3rem;
    margin-bottom: 4rem;
  }

  .hub-welcome {
    font-size: 4rem;
  }

  .hub-subtitle {
    font-size: 1.5rem;
  }

  .hub-quote {
    font-size: 1.25rem;
    padding: 1.5rem 3rem;
    max-width: 800px;
  }

  .hub-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }

  .hub-card {
    padding: 3rem;
  }

  .hub-card-icon {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  .hub-card-title {
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }

  .hub-card-description {
    font-size: 1.125rem;
  }

  .hub-bottom-decoration {
    margin-top: 5rem;
    padding: 3rem;
  }

  .hub-bottom-decoration .decoration-content {
    font-size: 1.375rem;
    gap: 1.5rem;
  }

  .hub-bottom-decoration .decoration-icon {
    font-size: 2rem;
  }
}

/* Bounce animation */
@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

/* Rotate animation */
@keyframes rotate-gentle {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.animate-rotate-gentle {
  animation: rotate-gentle 4s ease-in-out infinite;
}

/* Scale pulse */
@keyframes scale-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-scale-pulse {
  animation: scale-pulse 3s ease-in-out infinite;
}

/* ===== ADDITIONAL RESPONSIVE STYLES ===== */

/* Landscape Mobile (max-height: 500px and orientation: landscape) */
@media (max-height: 500px) and (orientation: landscape) {
  .hub-container {
    padding: 1rem 0.75rem;
  }

  .hub-header {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .hub-welcome {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }

  .hub-subtitle {
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
  }

  .hub-quote {
    font-size: 0.8rem;
    padding: 0.75rem 1rem;
  }

  .hub-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .hub-card {
    padding: 1rem;
  }

  .hub-card-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .hub-card-title {
    font-size: 1rem;
    margin-bottom: 0.375rem;
  }

  .hub-card-description {
    font-size: 0.8rem;
  }

  .hub-bottom-decoration {
    margin-top: 1.5rem;
    padding: 1rem;
  }
}

/* Very Small Screens (max-width: 320px) */
@media (max-width: 320px) {
  .hub-container {
    padding: 0.75rem 0.5rem;
  }

  .hub-header {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .hub-welcome {
    font-size: 1.75rem;
  }

  .hub-subtitle {
    font-size: 0.875rem;
  }

  .hub-quote {
    font-size: 0.8125rem;
    padding: 0.75rem;
  }

  .hub-card {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .hub-card-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .hub-card-title {
    font-size: 1.125rem;
  }

  .hub-card-description {
    font-size: 0.8125rem;
  }
}

/* ===== ACCESSIBILITY & PERFORMANCE ===== */

/* Focus states for accessibility */
.hub-card:focus {
  outline: 3px solid #007BFF;
  outline-offset: 2px;
}

.hub-card:focus:not(:focus-visible) {
  outline: none;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .hub-card,
  .hub-card-icon,
  .animate-fadeInUp,
  .animate-bounce-gentle {
    animation: none !important;
    transition: none !important;
  }

  .hub-card:hover {
    transform: none !important;
  }
}

/* Gradient background animation */
@keyframes gradient-bg {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-bg {
  background-size: 400% 400%;
  animation: gradient-bg 8s ease infinite;
}

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Stagger animation delays */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

.animate-delay-600 {
  animation-delay: 0.6s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .animate-blob {
    animation-duration: 10s;
  }
  
  .hub-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
}
